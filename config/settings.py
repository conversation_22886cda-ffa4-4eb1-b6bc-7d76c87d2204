"""
Configuration management for the Polygon MATIC Signal Bot
Handles environment variables, API keys, and trading parameters
"""

import os
from typing import Optional, Dict, Any
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Config:
    """Main configuration class for the Signal Bot"""
    
    def __init__(self):
        self._load_config()
    
    def _load_config(self):
        """Load all configuration from environment variables"""
        
        # API Keys
        self.DEEPSEEK_API_KEY = self._get_required_env("DEEPSEEK_API_KEY")
        self.POLYGON_RPC_URL = self._get_env("POLYGON_RPC_URL", "https://polygon-rpc.com")
        self.ALCHEMY_API_KEY = self._get_env("ALCHEMY_API_KEY")
        self.ALCHEMY_RPC_URL = self._get_env("ALCHEMY_RPC_URL", "https://polygon-mainnet.g.alchemy.com/v2/")
        self.COINGLASS_API_KEY = self._get_env("COINGLASS_API_KEY")
        
        # Database Configuration
        self.REDIS_URL = self._get_env("REDIS_URL", "redis://localhost:6379/0")
        self.SQLITE_DB_PATH = self._get_env("SQLITE_DB_PATH", "data/signal_bot.db")
        
        # Trading Configuration
        self.VIRTUAL_CAPITAL = float(self._get_env("VIRTUAL_CAPITAL", "50.0"))  # Default to $50 for small portfolio
        self.MAX_POSITION_SIZE = float(self._get_env("MAX_POSITION_SIZE", "0.4"))  # 40% max position for small portfolio
        self.MIN_TRADE_SIZE = float(self._get_env("MIN_TRADE_SIZE", "5.0"))  # Minimum $5 trades
        self.MAX_TRADE_SIZE = float(self._get_env("MAX_TRADE_SIZE", "25.0"))  # Maximum $25 trades
        self.SCAN_INTERVAL = int(self._get_env("SCAN_INTERVAL", "15"))
        self.CONFIDENCE_THRESHOLD = float(self._get_env("CONFIDENCE_THRESHOLD", "0.65"))  # Lower threshold for small portfolio
        self.TARGET_GAIN_PERCENT = float(self._get_env("TARGET_GAIN_PERCENT", "3.0"))  # 3% target for small trades
        self.STOP_LOSS_PERCENT = float(self._get_env("STOP_LOSS_PERCENT", "2.0"))  # 2% stop loss
        self.MAX_HOLD_TIME_MINUTES = int(self._get_env("MAX_HOLD_TIME_MINUTES", "15"))  # Longer holds for small portfolio
        self.MAX_DAILY_LOSS = float(self._get_env("MAX_DAILY_LOSS", "5.0"))  # Max $5 daily loss
        
        # Logging Configuration
        self.LOG_LEVEL = self._get_env("LOG_LEVEL", "INFO")
        self.LOG_FILE_PATH = self._get_env("LOG_FILE_PATH", "logs/signal_bot.log")
        self.TRADE_LOG_PATH = self._get_env("TRADE_LOG_PATH", "logs/trades.csv")
        
        # Feature Toggles
        self.ENABLE_STOP_LOSS = self._get_bool_env("ENABLE_STOP_LOSS", False)
        self.ENABLE_TREND_FILTER = self._get_bool_env("ENABLE_TREND_FILTER", False)
        self.ENABLE_VOLUME_CONFIRMATION = self._get_bool_env("ENABLE_VOLUME_CONFIRMATION", False)
        self.ENABLE_LIQUIDATION_HEATMAP = self._get_bool_env("ENABLE_LIQUIDATION_HEATMAP", False)
        
        # Model Configuration
        self.LSTM_MODEL_PATH = self._get_env("LSTM_MODEL_PATH", "data/models/lstm_model.h5")
        self.MODEL_RETRAIN_INTERVAL_HOURS = int(self._get_env("MODEL_RETRAIN_INTERVAL_HOURS", "24"))
        self.FEATURE_WINDOW_SIZE = int(self._get_env("FEATURE_WINDOW_SIZE", "100"))
        
        # DEX Configuration
        self.QUICKSWAP_SUBGRAPH_URL = self._get_env(
            "QUICKSWAP_SUBGRAPH_URL", 
            "https://api.thegraph.com/subgraphs/name/sameepsi/quickswap06"
        )
        self.UNISWAP_V3_SUBGRAPH_URL = self._get_env(
            "UNISWAP_V3_SUBGRAPH_URL",
            "https://api.thegraph.com/subgraphs/name/uniswap/uniswap-v3-polygon"
        )
        
        # Risk Management
        self.MAX_DAILY_TRADES = int(self._get_env("MAX_DAILY_TRADES", "50"))
        self.MAX_CONCURRENT_POSITIONS = int(self._get_env("MAX_CONCURRENT_POSITIONS", "5"))
        self.SLIPPAGE_TOLERANCE = float(self._get_env("SLIPPAGE_TOLERANCE", "0.001"))
        self.GAS_PRICE_GWEI = int(self._get_env("GAS_PRICE_GWEI", "30"))

        # Phase 4: Paper Trading Configuration
        self.INITIAL_BALANCE_USD = float(self._get_env("INITIAL_BALANCE_USD", "10000"))
        self.MAX_POSITION_SIZE_PCT = float(self._get_env("MAX_POSITION_SIZE_PCT", "10"))
        self.MAX_TOTAL_EXPOSURE_PCT = float(self._get_env("MAX_TOTAL_EXPOSURE_PCT", "70"))
        self.MIN_TRADE_SIZE_USD = float(self._get_env("MIN_TRADE_SIZE_USD", "50"))

        # Advanced Risk Management
        self.MAX_DAILY_LOSS_PCT = float(self._get_env("MAX_DAILY_LOSS_PCT", "5"))
        self.MAX_DRAWDOWN_PCT = float(self._get_env("MAX_DRAWDOWN_PCT", "15"))
        self.DEFAULT_STOP_LOSS_PCT = float(self._get_env("DEFAULT_STOP_LOSS_PCT", "2"))
        self.CONFIDENCE_THRESHOLD = float(self._get_env("CONFIDENCE_THRESHOLD", "0.6"))

        # Trading Costs Simulation
        self.BASE_SLIPPAGE_BPS = int(self._get_env("BASE_SLIPPAGE_BPS", "8"))
        self.GAS_COST_USD = float(self._get_env("GAS_COST_USD", "0.50"))
        
        # Asset Selection
        self.TOP_ASSETS_COUNT = int(self._get_env("TOP_ASSETS_COUNT", "10"))
        self.MIN_LIQUIDITY_USD = float(self._get_env("MIN_LIQUIDITY_USD", "100000"))
        self.MIN_VOLUME_MULTIPLIER = float(self._get_env("MIN_VOLUME_MULTIPLIER", "3.0"))
        
        # DeepSeek API Configuration
        self.DEEPSEEK_API_BASE = self._get_env("DEEPSEEK_API_BASE", "https://api.deepseek.com/v1")
        self.DEEPSEEK_MODEL = self._get_env("DEEPSEEK_MODEL", "deepseek-chat")
        self.DEEPSEEK_MAX_TOKENS = int(self._get_env("DEEPSEEK_MAX_TOKENS", "1000"))
        self.DEEPSEEK_TEMPERATURE = float(self._get_env("DEEPSEEK_TEMPERATURE", "0.1"))
        
        # LSTM Model Parameters
        self.LSTM_SEQUENCE_LENGTH = int(self._get_env("LSTM_SEQUENCE_LENGTH", "60"))
        self.LSTM_BATCH_SIZE = int(self._get_env("LSTM_BATCH_SIZE", "32"))
        self.LSTM_EPOCHS = int(self._get_env("LSTM_EPOCHS", "50"))
        self.LSTM_LEARNING_RATE = float(self._get_env("LSTM_LEARNING_RATE", "0.001"))
        
        # Timeframe Configuration
        self.TIMEFRAMES = ["1m", "5m", "15m"]
        self.PRIMARY_TIMEFRAME = "5m"
        
        # Validation
        self._validate_config()
    
    def _get_env(self, key: str, default: Optional[str] = None) -> str:
        """Get environment variable with optional default"""
        return os.getenv(key, default)
    
    def _get_required_env(self, key: str) -> str:
        """Get required environment variable, raise error if missing"""
        value = os.getenv(key)
        if value is None:
            raise ValueError(f"Required environment variable {key} is not set")
        return value
    
    def _get_bool_env(self, key: str, default: bool = False) -> bool:
        """Get boolean environment variable"""
        value = os.getenv(key, str(default)).lower()
        return value in ('true', '1', 'yes', 'on')
    
    def _validate_config(self):
        """Validate configuration values"""
        if self.VIRTUAL_CAPITAL <= 0:
            raise ValueError("VIRTUAL_CAPITAL must be positive")
        
        if not (0 < self.MAX_POSITION_SIZE <= 1):
            raise ValueError("MAX_POSITION_SIZE must be between 0 and 1")
        
        if not (0 < self.CONFIDENCE_THRESHOLD <= 1):
            raise ValueError("CONFIDENCE_THRESHOLD must be between 0 and 1")
        
        if self.TARGET_GAIN_PERCENT <= 0:
            raise ValueError("TARGET_GAIN_PERCENT must be positive")
        
        if self.MAX_HOLD_TIME_MINUTES <= 0:
            raise ValueError("MAX_HOLD_TIME_MINUTES must be positive")
        
        if self.SCAN_INTERVAL <= 0:
            raise ValueError("SCAN_INTERVAL must be positive")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary (excluding sensitive data)"""
        config_dict = {}
        for key, value in self.__dict__.items():
            if 'API_KEY' not in key and 'PASSWORD' not in key:
                config_dict[key] = value
        return config_dict
    
    def get_trading_params(self) -> Dict[str, Any]:
        """Get trading-specific parameters"""
        return {
            'virtual_capital': self.VIRTUAL_CAPITAL,
            'max_position_size': self.MAX_POSITION_SIZE,
            'confidence_threshold': self.CONFIDENCE_THRESHOLD,
            'target_gain_percent': self.TARGET_GAIN_PERCENT,
            'max_hold_time_minutes': self.MAX_HOLD_TIME_MINUTES,
            'slippage_tolerance': self.SLIPPAGE_TOLERANCE,
            'max_daily_trades': self.MAX_DAILY_TRADES,
            'max_concurrent_positions': self.MAX_CONCURRENT_POSITIONS
        }
    
    def get_model_params(self) -> Dict[str, Any]:
        """Get ML model parameters"""
        return {
            'lstm_sequence_length': self.LSTM_SEQUENCE_LENGTH,
            'lstm_batch_size': self.LSTM_BATCH_SIZE,
            'lstm_epochs': self.LSTM_EPOCHS,
            'lstm_learning_rate': self.LSTM_LEARNING_RATE,
            'feature_window_size': self.FEATURE_WINDOW_SIZE,
            'model_retrain_interval_hours': self.MODEL_RETRAIN_INTERVAL_HOURS
        }


# Global configuration instance
config = Config()
