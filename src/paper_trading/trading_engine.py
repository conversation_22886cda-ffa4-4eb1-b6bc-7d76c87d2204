"""
Realistic Paper Trading Engine - Phase 4 Implementation
Simulates realistic trade execution with slippage, gas costs, and market impact
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import random
import math

from config.settings import Config
from src.data_collectors.polygon_rpc import polygon_rpc
from src.data_collectors.price_aggregator import price_aggregator
from src.data_collectors.dex_collectors import quickswap_collector, uniswap_v3_collector


class MarketCondition(Enum):
    """Market condition types"""
    NORMAL = "normal"
    VOLATILE = "volatile"
    LOW_LIQUIDITY = "low_liquidity"
    HIGH_VOLUME = "high_volume"


@dataclass
class MarketImpact:
    """Market impact calculation result"""
    price_impact_pct: float
    slippage_pct: float
    liquidity_available: float
    execution_delay_ms: int
    partial_fill_risk: float


class TradingEngine:
    """Realistic paper trading execution engine"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Market simulation parameters
        self.base_slippage_bps = 8  # 0.08% base slippage
        self.volatility_multiplier = 1.5  # Slippage multiplier during volatility
        self.min_liquidity_threshold = 50000  # $50k minimum liquidity
        self.max_slippage_pct = 0.02  # 2% maximum slippage cap
        
        # Execution simulation
        self.execution_delay_range = (100, 500)  # 100-500ms execution delay
        self.partial_fill_threshold = 0.1  # 10% of liquidity for partial fills
        
        # Gas cost simulation
        self.base_gas_units = 150000  # Base gas for DEX swap
        self.gas_variance = 0.2  # 20% variance in gas usage
        
    async def simulate_trade_execution(
        self, 
        asset: str, 
        direction: str, 
        size_usd: float, 
        target_price: float
    ) -> Dict[str, Any]:
        """Simulate realistic trade execution"""
        
        try:
            # Get current market conditions
            market_conditions = await self._analyze_market_conditions(asset)
            
            # Calculate market impact
            market_impact = await self._calculate_market_impact(
                asset, size_usd, market_conditions
            )
            
            # Simulate execution delay
            execution_delay = await self._simulate_execution_delay(market_conditions)
            
            # Get final execution price with slippage
            execution_price = self._apply_slippage(
                target_price, direction, market_impact.slippage_pct
            )
            
            # Calculate gas costs
            gas_cost = await self._calculate_gas_cost(market_conditions)
            
            # Check for partial fills
            partial_fill_info = self._check_partial_fill(size_usd, market_impact)
            
            # Simulate execution success/failure
            execution_success = self._simulate_execution_success(market_conditions, size_usd)
            
            if not execution_success['success']:
                return {
                    'success': False,
                    'error': execution_success['error'],
                    'market_conditions': market_conditions,
                    'attempted_size': size_usd
                }
            
            # Calculate final quantities and costs
            executed_size = size_usd * partial_fill_info['fill_ratio']
            executed_quantity = executed_size / execution_price
            
            slippage_cost = abs(execution_price - target_price) * executed_quantity
            total_cost = executed_size + gas_cost + slippage_cost
            
            return {
                'success': True,
                'execution_price': execution_price,
                'executed_quantity': executed_quantity,
                'executed_size_usd': executed_size,
                'gas_cost': gas_cost,
                'slippage_cost': slippage_cost,
                'total_cost': total_cost,
                'execution_delay_ms': execution_delay,
                'market_impact': {
                    'price_impact_pct': market_impact.price_impact_pct,
                    'slippage_pct': market_impact.slippage_pct,
                    'liquidity_available': market_impact.liquidity_available
                },
                'partial_fill': {
                    'is_partial': partial_fill_info['is_partial'],
                    'fill_ratio': partial_fill_info['fill_ratio'],
                    'unfilled_size': size_usd - executed_size
                },
                'market_conditions': market_conditions
            }
            
        except Exception as e:
            self.logger.error(f"Trade execution simulation failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'attempted_size': size_usd
            }
    
    async def _analyze_market_conditions(self, asset: str) -> Dict[str, Any]:
        """Analyze current market conditions for the asset"""
        try:
            # Get market data from multiple sources
            token_address = self._get_token_address(asset)
            price_data = await price_aggregator.get_token_price(token_address, "USDC")
            
            if not price_data:
                return self._default_market_conditions()
            
            # Analyze liquidity
            total_liquidity = sum(
                source.get('liquidity_usd', 0) 
                for source in price_data.get('sources', [])
            )
            
            # Analyze volume
            total_volume_24h = sum(
                source.get('volume_24h_usd', 0) 
                for source in price_data.get('sources', [])
            )
            
            # Determine market condition
            condition = MarketCondition.NORMAL
            
            if total_liquidity < self.min_liquidity_threshold:
                condition = MarketCondition.LOW_LIQUIDITY
            elif total_volume_24h > total_liquidity * 2:  # High turnover
                condition = MarketCondition.HIGH_VOLUME
            elif price_data.get('price_std', 0) > price_data.get('price', 1) * 0.02:
                condition = MarketCondition.VOLATILE
            
            return {
                'condition': condition,
                'total_liquidity': total_liquidity,
                'volume_24h': total_volume_24h,
                'price_volatility': price_data.get('price_std', 0),
                'source_count': len(price_data.get('sources', [])),
                'confidence': price_data.get('confidence', 0.5)
            }
            
        except Exception as e:
            self.logger.warning(f"Market condition analysis failed: {e}")
            return self._default_market_conditions()
    
    def _default_market_conditions(self) -> Dict[str, Any]:
        """Return default market conditions when analysis fails"""
        return {
            'condition': MarketCondition.NORMAL,
            'total_liquidity': 100000,  # $100k default
            'volume_24h': 50000,
            'price_volatility': 0.01,
            'source_count': 1,
            'confidence': 0.3
        }
    
    async def _calculate_market_impact(
        self, 
        asset: str, 
        size_usd: float, 
        market_conditions: Dict[str, Any]
    ) -> MarketImpact:
        """Calculate market impact for the trade"""
        
        liquidity = market_conditions['total_liquidity']
        condition = market_conditions['condition']
        
        # Base price impact calculation
        liquidity_ratio = size_usd / liquidity if liquidity > 0 else 1.0
        base_impact = math.sqrt(liquidity_ratio) * 0.001  # Square root impact model
        
        # Adjust for market conditions
        condition_multipliers = {
            MarketCondition.NORMAL: 1.0,
            MarketCondition.VOLATILE: 1.8,
            MarketCondition.LOW_LIQUIDITY: 2.5,
            MarketCondition.HIGH_VOLUME: 0.8
        }
        
        impact_multiplier = condition_multipliers.get(condition, 1.0)
        price_impact = base_impact * impact_multiplier
        
        # Calculate slippage (price impact + base slippage)
        base_slippage = self.base_slippage_bps / 10000
        total_slippage = base_slippage + price_impact
        
        # Cap maximum slippage
        total_slippage = min(total_slippage, self.max_slippage_pct)
        
        # Estimate execution delay based on conditions
        base_delay = random.randint(*self.execution_delay_range)
        if condition == MarketCondition.VOLATILE:
            base_delay *= 1.5
        elif condition == MarketCondition.LOW_LIQUIDITY:
            base_delay *= 2.0
        
        # Calculate partial fill risk
        partial_fill_risk = min(liquidity_ratio / self.partial_fill_threshold, 1.0)
        
        return MarketImpact(
            price_impact_pct=price_impact,
            slippage_pct=total_slippage,
            liquidity_available=liquidity,
            execution_delay_ms=int(base_delay),
            partial_fill_risk=partial_fill_risk
        )
    
    async def _simulate_execution_delay(self, market_conditions: Dict[str, Any]) -> int:
        """Simulate realistic execution delay"""
        base_delay = random.randint(*self.execution_delay_range)
        
        # Adjust based on market conditions
        condition = market_conditions['condition']
        if condition == MarketCondition.VOLATILE:
            base_delay = int(base_delay * 1.5)
        elif condition == MarketCondition.LOW_LIQUIDITY:
            base_delay = int(base_delay * 2.0)
        elif condition == MarketCondition.HIGH_VOLUME:
            base_delay = int(base_delay * 0.8)
        
        # Add network congestion simulation
        if random.random() < 0.1:  # 10% chance of network congestion
            base_delay *= 3
        
        return base_delay
    
    def _apply_slippage(self, target_price: float, direction: str, slippage_pct: float) -> float:
        """Apply slippage to target price"""
        if direction.upper() == 'BUY':
            # Buying - price goes up due to slippage
            return target_price * (1 + slippage_pct)
        else:  # SELL
            # Selling - price goes down due to slippage
            return target_price * (1 - slippage_pct)
    
    async def _calculate_gas_cost(self, market_conditions: Dict[str, Any]) -> float:
        """Calculate realistic gas cost"""
        try:
            # Get current gas prices
            gas_prices = await polygon_rpc.get_gas_price()
            gas_price_gwei = gas_prices.get('fast', 30)
            
            # Estimate gas units with variance
            base_gas = self.base_gas_units
            gas_variance = random.uniform(-self.gas_variance, self.gas_variance)
            estimated_gas = int(base_gas * (1 + gas_variance))
            
            # Adjust for market conditions
            condition = market_conditions['condition']
            if condition == MarketCondition.VOLATILE:
                estimated_gas = int(estimated_gas * 1.2)  # More complex routing
            elif condition == MarketCondition.LOW_LIQUIDITY:
                estimated_gas = int(estimated_gas * 1.4)  # Multiple hops needed
            
            # Calculate cost in USD (approximate MATIC price)
            matic_price_usd = 0.80  # Approximate MATIC price
            gas_cost_matic = (gas_price_gwei * estimated_gas) / 1e9
            gas_cost_usd = gas_cost_matic * matic_price_usd
            
            return gas_cost_usd
            
        except Exception as e:
            self.logger.warning(f"Gas cost calculation failed: {e}")
            return 0.50  # Default gas cost
    
    def _check_partial_fill(self, size_usd: float, market_impact: MarketImpact) -> Dict[str, Any]:
        """Check if trade will be partially filled"""
        
        # Calculate if trade size exceeds comfortable liquidity threshold
        liquidity_ratio = size_usd / market_impact.liquidity_available
        
        if liquidity_ratio > self.partial_fill_threshold:
            # Partial fill likely
            fill_ratio = min(1.0, self.partial_fill_threshold / liquidity_ratio)
            fill_ratio = max(0.5, fill_ratio)  # Minimum 50% fill
            
            return {
                'is_partial': True,
                'fill_ratio': fill_ratio,
                'reason': 'insufficient_liquidity'
            }
        
        return {
            'is_partial': False,
            'fill_ratio': 1.0,
            'reason': None
        }
    
    def _simulate_execution_success(
        self, 
        market_conditions: Dict[str, Any], 
        size_usd: float
    ) -> Dict[str, Any]:
        """Simulate execution success/failure"""
        
        # Base success rate
        success_rate = 0.98  # 98% base success rate
        
        # Adjust based on market conditions
        condition = market_conditions['condition']
        if condition == MarketCondition.VOLATILE:
            success_rate *= 0.95  # 5% higher failure rate
        elif condition == MarketCondition.LOW_LIQUIDITY:
            success_rate *= 0.90  # 10% higher failure rate
        
        # Adjust based on trade size
        if size_usd > 10000:  # Large trades more likely to fail
            success_rate *= 0.95
        
        # Simulate random failure
        if random.random() > success_rate:
            failure_reasons = [
                'insufficient_liquidity',
                'price_moved_too_much',
                'network_congestion',
                'slippage_tolerance_exceeded'
            ]
            return {
                'success': False,
                'error': random.choice(failure_reasons)
            }
        
        return {'success': True, 'error': None}
    
    def _get_token_address(self, asset: str) -> str:
        """Get token address for asset"""
        # Simplified token mapping
        token_map = {
            'MATIC/USDC': '******************************************',  # WMATIC
            'WETH/USDC': '******************************************',  # WETH
            'USDT/USDC': '******************************************',  # USDT
        }
        
        return token_map.get(asset, '******************************************')


# Global trading engine instance
trading_engine = None

def get_trading_engine(config: Config = None) -> TradingEngine:
    """Get global trading engine instance"""
    global trading_engine
    if trading_engine is None and config:
        trading_engine = TradingEngine(config)
    return trading_engine
