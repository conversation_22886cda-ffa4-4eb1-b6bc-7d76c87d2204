"""
Advanced Risk Management System - Phase 4 Implementation
Dynamic risk controls, position sizing, and exposure management
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import statistics
import math

from config.settings import Config


class RiskLevel(Enum):
    """Risk level classifications"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    EXTREME = "extreme"


@dataclass
class RiskAssessment:
    """Risk assessment result"""
    risk_level: RiskLevel
    risk_score: float  # 0-100
    position_size_multiplier: float  # 0.0-1.0
    recommended_stop_loss: Optional[float]
    recommended_take_profit: Optional[float]
    max_hold_time: Optional[int]  # minutes
    warnings: List[str]
    reasoning: str


class RiskManager:
    """Advanced risk management system"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Risk parameters
        self.max_portfolio_risk_pct = 0.02  # 2% max portfolio risk per trade
        self.max_daily_loss_pct = 0.05  # 5% max daily loss
        self.max_drawdown_pct = 0.15  # 15% max drawdown before stopping
        
        # Position sizing parameters
        self.base_position_size_pct = 0.05  # 5% base position size
        self.max_position_size_pct = 0.15  # 15% max position size
        self.min_position_size_usd = 50  # $50 minimum position
        
        # Stop loss parameters
        self.default_stop_loss_pct = 0.02  # 2% default stop loss
        self.max_stop_loss_pct = 0.05  # 5% maximum stop loss
        self.trailing_stop_activation = 0.015  # 1.5% profit before trailing
        
        # Volatility-based adjustments
        self.volatility_lookback_hours = 24
        self.high_volatility_threshold = 0.03  # 3% hourly volatility
        
        # Correlation limits
        self.max_correlated_positions = 3
        self.correlation_threshold = 0.7
        
    def assess_trade_risk(
        self, 
        signal: Dict[str, Any], 
        portfolio_state: Dict[str, Any],
        market_conditions: Dict[str, Any]
    ) -> RiskAssessment:
        """Comprehensive risk assessment for a trade signal"""
        
        try:
            risk_factors = []
            warnings = []
            
            # 1. Signal quality risk
            signal_risk = self._assess_signal_risk(signal)
            risk_factors.append(signal_risk)
            
            # 2. Portfolio risk
            portfolio_risk = self._assess_portfolio_risk(signal, portfolio_state)
            risk_factors.append(portfolio_risk)
            
            # 3. Market condition risk
            market_risk = self._assess_market_risk(signal, market_conditions)
            risk_factors.append(market_risk)
            
            # 4. Correlation risk
            correlation_risk = self._assess_correlation_risk(signal, portfolio_state)
            risk_factors.append(correlation_risk)
            
            # 5. Timing risk
            timing_risk = self._assess_timing_risk(signal)
            risk_factors.append(timing_risk)
            
            # Calculate overall risk score
            risk_score = self._calculate_overall_risk(risk_factors)
            risk_level = self._determine_risk_level(risk_score)
            
            # Calculate position size multiplier
            position_multiplier = self._calculate_position_multiplier(risk_score, signal)
            
            # Generate stop loss and take profit recommendations
            stop_loss = self._calculate_dynamic_stop_loss(signal, risk_score, market_conditions)
            take_profit = self._calculate_take_profit(signal, risk_score)
            
            # Calculate max hold time
            max_hold_time = self._calculate_max_hold_time(signal, risk_score)
            
            # Collect warnings
            warnings.extend(self._generate_risk_warnings(risk_factors, risk_score))
            
            # Generate reasoning
            reasoning = self._generate_risk_reasoning(risk_factors, risk_score)
            
            return RiskAssessment(
                risk_level=risk_level,
                risk_score=risk_score,
                position_size_multiplier=position_multiplier,
                recommended_stop_loss=stop_loss,
                recommended_take_profit=take_profit,
                max_hold_time=max_hold_time,
                warnings=warnings,
                reasoning=reasoning
            )
            
        except Exception as e:
            self.logger.error(f"Risk assessment failed: {e}")
            return self._conservative_risk_assessment()
    
    def _assess_signal_risk(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """Assess risk based on signal quality"""
        confidence = signal.get('confidence', 0.5)
        signal_strength = signal.get('signal_strength', 0.5)
        
        # Lower confidence/strength = higher risk
        confidence_risk = (1 - confidence) * 30  # 0-30 points
        strength_risk = (1 - signal_strength) * 20  # 0-20 points
        
        # Check for conflicting signals
        lstm_predictions = signal.get('lstm_predictions', {})
        conflicting_signals = 0
        
        if lstm_predictions:
            directions = []
            for timeframe, pred in lstm_predictions.items():
                if pred and pred.get('predicted_change'):
                    directions.append(1 if pred['predicted_change'] > 0 else -1)
            
            if len(set(directions)) > 1:  # Conflicting directions
                conflicting_signals = 15
        
        total_risk = confidence_risk + strength_risk + conflicting_signals
        
        return {
            'category': 'signal_quality',
            'risk_score': min(total_risk, 50),  # Cap at 50
            'factors': {
                'confidence_risk': confidence_risk,
                'strength_risk': strength_risk,
                'conflicting_signals': conflicting_signals
            }
        }
    
    def _assess_portfolio_risk(self, signal: Dict[str, Any], portfolio_state: Dict[str, Any]) -> Dict[str, Any]:
        """Assess portfolio-level risk"""
        current_positions = len(portfolio_state.get('positions', {}).get('position_details', []))
        total_exposure_pct = portfolio_state.get('risk_metrics', {}).get('current_exposure_pct', 0)
        
        # Position concentration risk
        concentration_risk = 0
        if current_positions >= 5:
            concentration_risk = 10
        elif current_positions >= 8:
            concentration_risk = 20
        
        # Exposure risk
        exposure_risk = 0
        if total_exposure_pct > 70:
            exposure_risk = 15
        elif total_exposure_pct > 50:
            exposure_risk = 10
        
        # Check for existing position in same asset
        asset = signal.get('asset', '')
        existing_position_risk = 0
        for pos in portfolio_state.get('positions', {}).get('position_details', []):
            if pos.get('asset') == asset:
                existing_position_risk = 25  # High risk for duplicate positions
                break
        
        total_risk = concentration_risk + exposure_risk + existing_position_risk
        
        return {
            'category': 'portfolio',
            'risk_score': min(total_risk, 50),
            'factors': {
                'concentration_risk': concentration_risk,
                'exposure_risk': exposure_risk,
                'existing_position_risk': existing_position_risk
            }
        }
    
    def _assess_market_risk(self, signal: Dict[str, Any], market_conditions: Dict[str, Any]) -> Dict[str, Any]:
        """Assess market condition risk - adaptive to real conditions"""
        # Get market data with defaults
        volatility = market_conditions.get('volatility', 0.02)
        volume_24h = market_conditions.get('volume_24h', 1000000)
        price_change_24h = abs(market_conditions.get('price_change_24h', 0.0))
        liquidity_score = market_conditions.get('liquidity_score', 0.7)

        # Volatility risk - more nuanced assessment
        volatility_risk = 0
        if volatility > 0.15:  # Very high volatility
            volatility_risk = 25
        elif volatility > 0.08:  # High volatility
            volatility_risk = 15
        elif volatility > 0.04:  # Medium volatility
            volatility_risk = 8
        elif volatility < 0.005:  # Very low volatility (also risky - low activity)
            volatility_risk = 10

        # Volume/Liquidity risk - flow with market activity
        volume_risk = 0
        if volume_24h < 100000:  # Very low volume
            volume_risk = 20
        elif volume_24h < 500000:  # Low volume
            volume_risk = 12
        elif volume_24h > 10000000:  # Very high volume (potential manipulation)
            volume_risk = 8

        # Price movement risk - recent volatility
        price_movement_risk = 0
        if price_change_24h > 0.2:  # >20% change
            price_movement_risk = 20
        elif price_change_24h > 0.1:  # >10% change
            price_movement_risk = 12
        elif price_change_24h > 0.05:  # >5% change
            price_movement_risk = 6

        # Liquidity score risk
        liquidity_risk = max(0, (0.5 - liquidity_score) * 40)  # Scale 0.5-0 to 0-20 risk

        total_risk = volatility_risk + volume_risk + price_movement_risk + liquidity_risk

        return {
            'category': 'market_conditions',
            'risk_score': min(total_risk, 60),  # Allow higher market risk scores
            'factors': {
                'volatility_risk': volatility_risk,
                'volume_risk': volume_risk,
                'price_movement_risk': price_movement_risk,
                'liquidity_risk': liquidity_risk
            }
        }
    
    def _assess_correlation_risk(self, signal: Dict[str, Any], portfolio_state: Dict[str, Any]) -> Dict[str, Any]:
        """Assess correlation risk with existing positions"""
        # Simplified correlation assessment
        # In practice, this would use actual price correlation data
        
        asset = signal.get('asset', '')
        existing_assets = [
            pos.get('asset', '') 
            for pos in portfolio_state.get('positions', {}).get('position_details', [])
        ]
        
        correlation_risk = 0
        
        # Simple asset correlation heuristics
        if 'MATIC' in asset:
            correlated_count = sum(1 for a in existing_assets if 'MATIC' in a or 'WETH' in a)
        elif 'WETH' in asset:
            correlated_count = sum(1 for a in existing_assets if 'WETH' in a or 'MATIC' in a)
        else:
            correlated_count = sum(1 for a in existing_assets if a.split('/')[0] == asset.split('/')[0])
        
        if correlated_count >= 2:
            correlation_risk = 20
        elif correlated_count >= 1:
            correlation_risk = 10
        
        return {
            'category': 'correlation',
            'risk_score': correlation_risk,
            'factors': {
                'correlated_positions': correlated_count
            }
        }
    
    def _assess_timing_risk(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """Assess timing-based risk factors"""
        current_time = datetime.now()
        
        # Weekend risk (crypto markets are 24/7 but lower liquidity on weekends)
        weekend_risk = 0
        if current_time.weekday() >= 5:  # Saturday or Sunday
            weekend_risk = 10
        
        # Late night/early morning risk (lower liquidity)
        hour_risk = 0
        if current_time.hour < 6 or current_time.hour > 22:
            hour_risk = 5
        
        # Signal age risk
        signal_timestamp = signal.get('timestamp')
        age_risk = 0
        if signal_timestamp:
            try:
                signal_time = datetime.fromisoformat(signal_timestamp.replace('Z', '+00:00'))
                age_minutes = (current_time - signal_time.replace(tzinfo=None)).total_seconds() / 60
                if age_minutes > 5:  # Signal older than 5 minutes
                    age_risk = min(age_minutes * 2, 20)
            except:
                age_risk = 10  # Unknown age = moderate risk
        
        total_risk = weekend_risk + hour_risk + age_risk
        
        return {
            'category': 'timing',
            'risk_score': min(total_risk, 30),
            'factors': {
                'weekend_risk': weekend_risk,
                'hour_risk': hour_risk,
                'age_risk': age_risk
            }
        }
    
    def _calculate_overall_risk(self, risk_factors: List[Dict[str, Any]]) -> float:
        """Calculate overall risk score from individual factors"""
        total_risk = sum(factor['risk_score'] for factor in risk_factors)
        
        # Apply non-linear scaling to emphasize high-risk combinations
        if total_risk > 100:
            total_risk = 100 + math.sqrt(total_risk - 100) * 10
        
        return min(total_risk, 200)  # Cap at 200
    
    def _determine_risk_level(self, risk_score: float) -> RiskLevel:
        """Determine risk level from risk score - more balanced thresholds"""
        if risk_score <= 25:
            return RiskLevel.LOW
        elif risk_score <= 55:
            return RiskLevel.MEDIUM
        elif risk_score <= 85:
            return RiskLevel.HIGH
        else:
            return RiskLevel.EXTREME
    
    def _calculate_position_multiplier(self, risk_score: float, signal: Dict[str, Any]) -> float:
        """Calculate adaptive position size multiplier - flow with market conditions"""
        # More adaptive base multiplier - less conservative, more responsive
        if risk_score <= 20:
            base_multiplier = 1.0  # Full position for very low risk
        elif risk_score <= 40:
            base_multiplier = 0.85  # Strong position for low-medium risk
        elif risk_score <= 60:
            base_multiplier = 0.7   # Moderate position for medium risk
        elif risk_score <= 80:
            base_multiplier = 0.5   # Reduced position for high risk
        elif risk_score <= 100:
            base_multiplier = 0.3   # Small position for very high risk
        else:
            base_multiplier = 0.15  # Minimal position for extreme risk

        # Enhance with signal quality - reward strong signals
        confidence = signal.get('confidence', 0.5)
        signal_strength = signal.get('signal_strength', 0.5)

        # More aggressive quality adjustment - good signals get rewarded
        quality_score = (confidence * 0.6 + signal_strength * 0.4)
        quality_multiplier = 0.7 + (quality_score * 0.6)  # Range: 0.7 to 1.3

        # Flow with market momentum - adapt to conditions
        momentum_factor = 1.0
        if 'price_momentum' in signal:
            momentum = abs(signal['price_momentum'])
            if momentum > 0.02:  # Strong momentum
                momentum_factor = 1.1  # Slightly increase position
            elif momentum < 0.005:  # Weak momentum
                momentum_factor = 0.9  # Slightly decrease position

        final_multiplier = base_multiplier * quality_multiplier * momentum_factor
        return max(0.15, min(1.0, final_multiplier))
    
    def _calculate_dynamic_stop_loss(
        self, 
        signal: Dict[str, Any], 
        risk_score: float, 
        market_conditions: Dict[str, Any]
    ) -> Optional[float]:
        """Calculate dynamic stop loss based on risk and market conditions"""
        
        entry_price = signal.get('entry_price', 0)
        if entry_price <= 0:
            return None
        
        # Base stop loss percentage
        base_stop_pct = self.default_stop_loss_pct
        
        # Adjust for risk score
        if risk_score > 60:
            base_stop_pct *= 0.8  # Tighter stop for high risk
        elif risk_score < 30:
            base_stop_pct *= 1.2  # Wider stop for low risk
        
        # Adjust for market volatility
        volatility = market_conditions.get('price_volatility', 0.01)
        if volatility > self.high_volatility_threshold:
            base_stop_pct *= (1 + volatility * 10)  # Wider stop in volatile markets
        
        # Cap stop loss
        final_stop_pct = min(base_stop_pct, self.max_stop_loss_pct)
        
        # Calculate stop loss price
        if signal.get('signal_direction') == 'BUY':
            stop_loss_price = entry_price * (1 - final_stop_pct)
        else:  # SELL
            stop_loss_price = entry_price * (1 + final_stop_pct)
        
        return stop_loss_price
    
    def _calculate_take_profit(self, signal: Dict[str, Any], risk_score: float) -> Optional[float]:
        """Calculate take profit target"""
        target_price = signal.get('target_price')
        if target_price:
            return target_price
        
        entry_price = signal.get('entry_price', 0)
        if entry_price <= 0:
            return None
        
        # Base take profit (risk-reward ratio of 2:1)
        base_profit_pct = self.default_stop_loss_pct * 2
        
        # Adjust for risk score (lower risk = higher target)
        if risk_score < 30:
            base_profit_pct *= 1.5
        elif risk_score > 60:
            base_profit_pct *= 0.8
        
        # Calculate take profit price
        if signal.get('signal_direction') == 'BUY':
            take_profit_price = entry_price * (1 + base_profit_pct)
        else:  # SELL
            take_profit_price = entry_price * (1 - base_profit_pct)
        
        return take_profit_price
    
    def _calculate_max_hold_time(self, signal: Dict[str, Any], risk_score: float) -> Optional[int]:
        """Calculate maximum hold time in minutes"""
        base_hold_time = signal.get('max_hold_time', 15)  # 15 minutes default
        
        # Adjust for risk score
        if risk_score > 60:
            base_hold_time = int(base_hold_time * 0.7)  # Shorter hold for high risk
        elif risk_score < 30:
            base_hold_time = int(base_hold_time * 1.3)  # Longer hold for low risk
        
        return max(5, min(base_hold_time, 30))  # 5-30 minute range
    
    def _generate_risk_warnings(self, risk_factors: List[Dict[str, Any]], risk_score: float) -> List[str]:
        """Generate risk warnings"""
        warnings = []
        
        if risk_score > 100:
            warnings.append("EXTREME RISK: Consider avoiding this trade")
        elif risk_score > 60:
            warnings.append("HIGH RISK: Use reduced position size")
        
        for factor in risk_factors:
            if factor['risk_score'] > 20:
                category = factor['category']
                warnings.append(f"Elevated {category} risk detected")
        
        return warnings
    
    def _generate_risk_reasoning(self, risk_factors: List[Dict[str, Any]], risk_score: float) -> str:
        """Generate risk assessment reasoning"""
        high_risk_factors = [f for f in risk_factors if f['risk_score'] > 15]
        
        if not high_risk_factors:
            return "Low risk trade with favorable conditions"
        
        reasoning_parts = []
        for factor in high_risk_factors:
            category = factor['category']
            score = factor['risk_score']
            reasoning_parts.append(f"{category} risk ({score:.0f} points)")
        
        return f"Risk factors: {', '.join(reasoning_parts)}"
    
    def _conservative_risk_assessment(self) -> RiskAssessment:
        """Return conservative risk assessment when calculation fails"""
        return RiskAssessment(
            risk_level=RiskLevel.HIGH,
            risk_score=80,
            position_size_multiplier=0.3,
            recommended_stop_loss=None,
            recommended_take_profit=None,
            max_hold_time=10,
            warnings=["Risk assessment failed - using conservative settings"],
            reasoning="Unable to calculate risk - defaulting to conservative approach"
        )
