"""
Performance Analytics and Backtesting Framework - Phase 4 Implementation
Comprehensive analysis of trading performance with advanced metrics
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import statistics
import math
import json

from config.settings import Config


@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics"""
    # Return metrics
    total_return: float
    total_return_pct: float
    annualized_return_pct: float
    
    # Risk metrics
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    max_drawdown_pct: float
    
    # Trade metrics
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate_pct: float
    
    # Profit metrics
    avg_trade_pnl: float
    avg_winning_trade: float
    avg_losing_trade: float
    profit_factor: float
    
    # Time metrics
    avg_hold_time_minutes: float
    max_hold_time_minutes: float
    min_hold_time_minutes: float
    
    # Cost metrics
    total_fees: float
    avg_slippage_pct: float
    avg_gas_cost: float


class PerformanceAnalytics:
    """Advanced performance analytics and backtesting"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Risk-free rate for Sharpe ratio (approximate)
        self.risk_free_rate = 0.02  # 2% annual risk-free rate
        
    def calculate_performance_metrics(
        self, 
        trade_history: List[Dict[str, Any]], 
        initial_balance: float,
        current_balance: float
    ) -> PerformanceMetrics:
        """Calculate comprehensive performance metrics"""
        
        if not trade_history:
            return self._empty_metrics()
        
        # Basic calculations
        total_trades = len(trade_history)
        winning_trades = len([t for t in trade_history if t['realized_pnl'] > 0])
        losing_trades = total_trades - winning_trades
        
        # Return calculations
        total_return = current_balance - initial_balance
        total_return_pct = (total_return / initial_balance) * 100
        
        # Time-based calculations
        first_trade = min(trade_history, key=lambda x: x['entry_time'])
        last_trade = max(trade_history, key=lambda x: x['exit_time'])
        
        trading_period_days = (
            datetime.fromisoformat(last_trade['exit_time']) - 
            datetime.fromisoformat(first_trade['entry_time'])
        ).days
        
        if trading_period_days > 0:
            annualized_return = (total_return_pct / 100) * (365 / trading_period_days)
        else:
            annualized_return = 0
        
        # Trade PnL analysis
        pnl_values = [t['realized_pnl'] for t in trade_history]
        winning_pnl = [pnl for pnl in pnl_values if pnl > 0]
        losing_pnl = [pnl for pnl in pnl_values if pnl <= 0]
        
        avg_trade_pnl = statistics.mean(pnl_values)
        avg_winning_trade = statistics.mean(winning_pnl) if winning_pnl else 0
        avg_losing_trade = statistics.mean(losing_pnl) if losing_pnl else 0
        
        # Profit factor
        gross_profit = sum(winning_pnl) if winning_pnl else 0
        gross_loss = abs(sum(losing_pnl)) if losing_pnl else 0
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        # Volatility and Sharpe ratio
        if len(pnl_values) > 1:
            volatility = statistics.stdev(pnl_values)
            if volatility > 0:
                sharpe_ratio = (avg_trade_pnl - (self.risk_free_rate / 252)) / volatility
            else:
                sharpe_ratio = 0
        else:
            volatility = 0
            sharpe_ratio = 0
        
        # Drawdown calculation
        drawdown_info = self._calculate_drawdown(trade_history, initial_balance)
        
        # Hold time analysis
        hold_times = [t['hold_time_minutes'] for t in trade_history]
        avg_hold_time = statistics.mean(hold_times)
        max_hold_time = max(hold_times)
        min_hold_time = min(hold_times)
        
        # Cost analysis
        total_fees = sum(t['gas_cost'] + t['slippage_cost'] for t in trade_history)
        avg_slippage_pct = statistics.mean([
            (t['slippage_cost'] / (t['quantity'] * t['entry_price'])) * 100 
            for t in trade_history if t['quantity'] > 0 and t['entry_price'] > 0
        ]) if trade_history else 0
        avg_gas_cost = statistics.mean([t['gas_cost'] for t in trade_history])
        
        return PerformanceMetrics(
            total_return=total_return,
            total_return_pct=total_return_pct,
            annualized_return_pct=annualized_return * 100,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=drawdown_info['max_drawdown'],
            max_drawdown_pct=drawdown_info['max_drawdown_pct'],
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            win_rate_pct=(winning_trades / total_trades) * 100,
            avg_trade_pnl=avg_trade_pnl,
            avg_winning_trade=avg_winning_trade,
            avg_losing_trade=avg_losing_trade,
            profit_factor=profit_factor,
            avg_hold_time_minutes=avg_hold_time,
            max_hold_time_minutes=max_hold_time,
            min_hold_time_minutes=min_hold_time,
            total_fees=total_fees,
            avg_slippage_pct=avg_slippage_pct,
            avg_gas_cost=avg_gas_cost
        )
    
    def _calculate_drawdown(
        self, 
        trade_history: List[Dict[str, Any]], 
        initial_balance: float
    ) -> Dict[str, float]:
        """Calculate maximum drawdown"""
        
        # Build equity curve
        equity_curve = [initial_balance]
        running_balance = initial_balance
        
        for trade in sorted(trade_history, key=lambda x: x['exit_time']):
            running_balance += trade['realized_pnl']
            equity_curve.append(running_balance)
        
        # Calculate drawdown
        peak = equity_curve[0]
        max_drawdown = 0
        max_drawdown_pct = 0
        
        for balance in equity_curve:
            if balance > peak:
                peak = balance
            
            drawdown = peak - balance
            drawdown_pct = (drawdown / peak) * 100 if peak > 0 else 0
            
            if drawdown > max_drawdown:
                max_drawdown = drawdown
                max_drawdown_pct = drawdown_pct
        
        return {
            'max_drawdown': max_drawdown,
            'max_drawdown_pct': max_drawdown_pct
        }
    
    def analyze_trading_patterns(self, trade_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze trading patterns and behaviors"""
        
        if not trade_history:
            return {}
        
        # Time-based analysis
        hourly_performance = self._analyze_hourly_performance(trade_history)
        daily_performance = self._analyze_daily_performance(trade_history)
        
        # Asset analysis
        asset_performance = self._analyze_asset_performance(trade_history)
        
        # Hold time analysis
        hold_time_analysis = self._analyze_hold_times(trade_history)
        
        # Exit reason analysis
        exit_reason_analysis = self._analyze_exit_reasons(trade_history)
        
        # Consecutive trades analysis
        streak_analysis = self._analyze_win_loss_streaks(trade_history)
        
        return {
            'hourly_performance': hourly_performance,
            'daily_performance': daily_performance,
            'asset_performance': asset_performance,
            'hold_time_analysis': hold_time_analysis,
            'exit_reason_analysis': exit_reason_analysis,
            'streak_analysis': streak_analysis
        }
    
    def _analyze_hourly_performance(self, trade_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze performance by hour of day"""
        hourly_pnl = {}
        
        for trade in trade_history:
            hour = datetime.fromisoformat(trade['entry_time']).hour
            if hour not in hourly_pnl:
                hourly_pnl[hour] = []
            hourly_pnl[hour].append(trade['realized_pnl'])
        
        hourly_stats = {}
        for hour, pnl_list in hourly_pnl.items():
            hourly_stats[hour] = {
                'avg_pnl': statistics.mean(pnl_list),
                'total_pnl': sum(pnl_list),
                'trade_count': len(pnl_list),
                'win_rate': len([p for p in pnl_list if p > 0]) / len(pnl_list) * 100
            }
        
        return hourly_stats
    
    def _analyze_daily_performance(self, trade_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze performance by day of week"""
        daily_pnl = {}
        
        for trade in trade_history:
            day = datetime.fromisoformat(trade['entry_time']).strftime('%A')
            if day not in daily_pnl:
                daily_pnl[day] = []
            daily_pnl[day].append(trade['realized_pnl'])
        
        daily_stats = {}
        for day, pnl_list in daily_pnl.items():
            daily_stats[day] = {
                'avg_pnl': statistics.mean(pnl_list),
                'total_pnl': sum(pnl_list),
                'trade_count': len(pnl_list),
                'win_rate': len([p for p in pnl_list if p > 0]) / len(pnl_list) * 100
            }
        
        return daily_stats
    
    def _analyze_asset_performance(self, trade_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze performance by asset"""
        asset_pnl = {}
        
        for trade in trade_history:
            asset = trade['asset']
            if asset not in asset_pnl:
                asset_pnl[asset] = []
            asset_pnl[asset].append(trade['realized_pnl'])
        
        asset_stats = {}
        for asset, pnl_list in asset_pnl.items():
            asset_stats[asset] = {
                'avg_pnl': statistics.mean(pnl_list),
                'total_pnl': sum(pnl_list),
                'trade_count': len(pnl_list),
                'win_rate': len([p for p in pnl_list if p > 0]) / len(pnl_list) * 100,
                'best_trade': max(pnl_list),
                'worst_trade': min(pnl_list)
            }
        
        return asset_stats
    
    def _analyze_hold_times(self, trade_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze hold time patterns"""
        hold_times = [t['hold_time_minutes'] for t in trade_history]
        
        # Categorize hold times
        short_term = [t for t in trade_history if t['hold_time_minutes'] <= 5]
        medium_term = [t for t in trade_history if 5 < t['hold_time_minutes'] <= 15]
        long_term = [t for t in trade_history if t['hold_time_minutes'] > 15]
        
        return {
            'avg_hold_time': statistics.mean(hold_times),
            'median_hold_time': statistics.median(hold_times),
            'short_term_trades': {
                'count': len(short_term),
                'avg_pnl': statistics.mean([t['realized_pnl'] for t in short_term]) if short_term else 0,
                'win_rate': len([t for t in short_term if t['realized_pnl'] > 0]) / len(short_term) * 100 if short_term else 0
            },
            'medium_term_trades': {
                'count': len(medium_term),
                'avg_pnl': statistics.mean([t['realized_pnl'] for t in medium_term]) if medium_term else 0,
                'win_rate': len([t for t in medium_term if t['realized_pnl'] > 0]) / len(medium_term) * 100 if medium_term else 0
            },
            'long_term_trades': {
                'count': len(long_term),
                'avg_pnl': statistics.mean([t['realized_pnl'] for t in long_term]) if long_term else 0,
                'win_rate': len([t for t in long_term if t['realized_pnl'] > 0]) / len(long_term) * 100 if long_term else 0
            }
        }
    
    def _analyze_exit_reasons(self, trade_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze exit reason patterns"""
        exit_reasons = {}
        
        for trade in trade_history:
            reason = trade['exit_reason']
            if reason not in exit_reasons:
                exit_reasons[reason] = []
            exit_reasons[reason].append(trade['realized_pnl'])
        
        exit_stats = {}
        for reason, pnl_list in exit_reasons.items():
            exit_stats[reason] = {
                'count': len(pnl_list),
                'avg_pnl': statistics.mean(pnl_list),
                'total_pnl': sum(pnl_list),
                'win_rate': len([p for p in pnl_list if p > 0]) / len(pnl_list) * 100
            }
        
        return exit_stats
    
    def _analyze_win_loss_streaks(self, trade_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze consecutive win/loss streaks"""
        if not trade_history:
            return {}
        
        # Sort by exit time
        sorted_trades = sorted(trade_history, key=lambda x: x['exit_time'])
        
        current_streak = 0
        current_streak_type = None
        max_win_streak = 0
        max_loss_streak = 0
        streaks = []
        
        for trade in sorted_trades:
            is_win = trade['realized_pnl'] > 0
            
            if current_streak_type is None:
                current_streak_type = 'win' if is_win else 'loss'
                current_streak = 1
            elif (current_streak_type == 'win' and is_win) or (current_streak_type == 'loss' and not is_win):
                current_streak += 1
            else:
                # Streak ended
                streaks.append({'type': current_streak_type, 'length': current_streak})
                
                if current_streak_type == 'win':
                    max_win_streak = max(max_win_streak, current_streak)
                else:
                    max_loss_streak = max(max_loss_streak, current_streak)
                
                current_streak_type = 'win' if is_win else 'loss'
                current_streak = 1
        
        # Handle final streak
        if current_streak_type:
            streaks.append({'type': current_streak_type, 'length': current_streak})
            if current_streak_type == 'win':
                max_win_streak = max(max_win_streak, current_streak)
            else:
                max_loss_streak = max(max_loss_streak, current_streak)
        
        return {
            'max_win_streak': max_win_streak,
            'max_loss_streak': max_loss_streak,
            'total_streaks': len(streaks),
            'avg_streak_length': statistics.mean([s['length'] for s in streaks]) if streaks else 0
        }
    
    def _empty_metrics(self) -> PerformanceMetrics:
        """Return empty performance metrics"""
        return PerformanceMetrics(
            total_return=0, total_return_pct=0, annualized_return_pct=0,
            volatility=0, sharpe_ratio=0, max_drawdown=0, max_drawdown_pct=0,
            total_trades=0, winning_trades=0, losing_trades=0, win_rate_pct=0,
            avg_trade_pnl=0, avg_winning_trade=0, avg_losing_trade=0, profit_factor=0,
            avg_hold_time_minutes=0, max_hold_time_minutes=0, min_hold_time_minutes=0,
            total_fees=0, avg_slippage_pct=0, avg_gas_cost=0
        )
    
    def generate_performance_report(
        self, 
        metrics: PerformanceMetrics, 
        patterns: Dict[str, Any]
    ) -> str:
        """Generate comprehensive performance report"""
        
        report = f"""
# Trading Performance Report
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Summary Metrics
- Total Return: ${metrics.total_return:.2f} ({metrics.total_return_pct:.2f}%)
- Annualized Return: {metrics.annualized_return_pct:.2f}%
- Sharpe Ratio: {metrics.sharpe_ratio:.3f}
- Maximum Drawdown: ${metrics.max_drawdown:.2f} ({metrics.max_drawdown_pct:.2f}%)

## Trading Statistics
- Total Trades: {metrics.total_trades}
- Win Rate: {metrics.win_rate_pct:.1f}% ({metrics.winning_trades}/{metrics.total_trades})
- Average Trade P&L: ${metrics.avg_trade_pnl:.2f}
- Average Winning Trade: ${metrics.avg_winning_trade:.2f}
- Average Losing Trade: ${metrics.avg_losing_trade:.2f}
- Profit Factor: {metrics.profit_factor:.2f}

## Time Analysis
- Average Hold Time: {metrics.avg_hold_time_minutes:.1f} minutes
- Max Hold Time: {metrics.max_hold_time_minutes:.1f} minutes
- Min Hold Time: {metrics.min_hold_time_minutes:.1f} minutes

## Cost Analysis
- Total Fees: ${metrics.total_fees:.2f}
- Average Slippage: {metrics.avg_slippage_pct:.3f}%
- Average Gas Cost: ${metrics.avg_gas_cost:.2f}

## Pattern Analysis
{json.dumps(patterns, indent=2)}
"""
        
        return report
