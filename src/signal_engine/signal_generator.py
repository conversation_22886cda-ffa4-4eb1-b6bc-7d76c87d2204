"""
AI-Powered Signal Generation Engine - Phase 3 Implementation
Combines LSTM predictions with DeepSeek AI analysis for trading signals
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from config.settings import Config
from src.ml_models.inference_engine import inference_engine, TradingSignal
from src.data_collectors.asset_selector import asset_selector
from src.data_collectors.price_aggregator import price_aggregator


class SignalGenerator:
    """AI-powered signal generation using LSTM and DeepSeek"""

    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Signal generation parameters
        self.min_confidence = config.CONFIDENCE_THRESHOLD
        self.max_signals_per_cycle = 10

        # Asset filtering parameters
        self.min_liquidity = config.MIN_LIQUIDITY_USD
        self.min_volume_multiplier = config.MIN_VOLUME_MULTIPLIER

    async def scan_assets(self) -> Dict[str, Any]:
        """Scan and filter assets using Phase 2 data infrastructure"""

        try:
            self.logger.info("Scanning assets for trading opportunities")

            # Get top assets from asset selector
            top_assets = await asset_selector.get_top_assets()

            if not top_assets:
                self.logger.warning("No assets returned from asset selector")
                return {'assets': [], 'total_scanned': 0}

            # Filter assets based on our criteria
            filtered_assets = []

            for asset in top_assets:
                try:
                    # Extract asset information
                    asset_symbol = asset.get('trading_token', {}).get('symbol', 'UNKNOWN')
                    liquidity_usd = asset.get('liquidity_usd', 0)
                    volume_24h = asset.get('volume_24h_usd', 0)
                    avg_volume = asset.get('avg_volume_7d', volume_24h)  # Use current volume as fallback

                    # Apply filters
                    if liquidity_usd < self.min_liquidity:
                        continue

                    # Use volume to liquidity ratio instead since we don't have historical data
                    volume_ratio = asset.get('volume_to_liquidity_ratio', 1.0)
                    if volume_ratio < 0.1:  # More lenient threshold
                        continue

                    # Add to filtered list
                    filtered_assets.append({
                        'symbol': asset_symbol,
                        'address': asset.get('trading_token', {}).get('address'),
                        'liquidity_usd': liquidity_usd,
                        'volume_24h': volume_24h,
                        'volume_ratio': volume_ratio,
                        'sentiment_score': asset.get('sentiment_score', 0.5),
                        'raw_data': asset
                    })

                except Exception as e:
                    self.logger.warning(f"Error processing asset {asset}: {e}")
                    continue

            # Sort by combined score (liquidity + volume + sentiment)
            filtered_assets.sort(
                key=lambda x: (x['liquidity_usd'] * x['volume_ratio'] * x['sentiment_score']),
                reverse=True
            )

            # Limit to top assets
            top_filtered = filtered_assets[:self.config.TOP_ASSETS_COUNT]

            self.logger.info(f"Asset scan completed: {len(top_filtered)}/{len(top_assets)} assets passed filters")

            return {
                'assets': top_filtered,
                'total_scanned': len(top_assets),
                'total_filtered': len(top_filtered),
                'scan_timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Asset scanning failed: {e}")
            return {'assets': [], 'total_scanned': 0, 'error': str(e)}

    async def generate_signals(self, scan_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate AI-powered trading signals"""

        assets = scan_result.get('assets', [])
        if not assets:
            self.logger.info("No assets to analyze for signals")
            return []

        self.logger.info(f"Generating signals for {len(assets)} assets")

        signals = []
        processed_count = 0

        try:
            # Process assets in batches for efficiency
            batch_size = 5
            for i in range(0, len(assets), batch_size):
                batch = assets[i:i + batch_size]

                # Generate signals for batch
                batch_signals = await self._process_asset_batch(batch)

                # Filter and add valid signals
                for signal_data in batch_signals:
                    if signal_data and self._is_valid_signal(signal_data):
                        signals.append(self._format_signal_output(signal_data))

                processed_count += len(batch)

                # Respect rate limits
                if i + batch_size < len(assets):
                    await asyncio.sleep(1)

                # Stop if we have enough signals
                if len(signals) >= self.max_signals_per_cycle:
                    break

            # Sort signals by strength and confidence
            signals.sort(
                key=lambda x: x['signal_strength'] * x['confidence'],
                reverse=True
            )

            self.logger.info(f"Signal generation completed: {len(signals)} signals from {processed_count} assets")

            return signals[:self.max_signals_per_cycle]

        except Exception as e:
            self.logger.error(f"Signal generation failed: {e}")
            return []

    async def _process_asset_batch(self, assets: List[Dict[str, Any]]) -> List[Optional[TradingSignal]]:
        """Process a batch of assets for signal generation"""

        tasks = []

        for asset in assets:
            task = self._generate_asset_signal(asset)
            tasks.append(task)

        # Execute batch with error handling
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results
        signals = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.warning(f"Failed to generate signal for {assets[i]['symbol']}: {result}")
                signals.append(None)
            else:
                signals.append(result)

        return signals

    async def _generate_asset_signal(self, asset: Dict[str, Any]) -> Optional[TradingSignal]:
        """Generate signal for a single asset"""

        try:
            symbol = asset['symbol']

            # Get recent market data for the asset
            market_data = await self._get_asset_market_data(asset)

            if market_data is None:
                self.logger.warning(f"No market data available for {symbol}")
                return None

            # Generate signal using inference engine
            signal = await inference_engine.generate_signal(symbol, market_data)

            return signal

        except Exception as e:
            self.logger.error(f"Failed to generate signal for {asset['symbol']}: {e}")
            return None

    async def _get_asset_market_data(self, asset: Dict[str, Any]) -> Optional[Any]:
        """Get market data for asset analysis"""

        try:
            # Get token address
            token_address = asset.get('address')
            if not token_address:
                return None

            # Get recent price history (last 2 hours of 1-minute data)
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=2)

            market_data = await price_aggregator.get_historical_prices(
                asset['symbol'],
                start_time,
                end_time,
                interval='1m'
            )

            return market_data

        except Exception as e:
            self.logger.error(f"Failed to get market data for {asset['symbol']}: {e}")
            return None

    def _is_valid_signal(self, signal: TradingSignal) -> bool:
        """Validate if signal meets our criteria"""

        if signal is None:
            return False

        # Check confidence threshold
        if signal.confidence < self.min_confidence:
            return False

        # Check signal direction (must be BUY or SELL, not HOLD)
        if signal.signal_direction == "HOLD":
            return False

        # Check signal strength
        if signal.signal_strength < 0.5:
            return False

        # Check if we have valid predictions
        if signal.ensemble_prediction is None:
            return False

        return True

    def _format_signal_output(self, signal: TradingSignal) -> Dict[str, Any]:
        """Format signal for output to main bot"""

        return {
            'asset': signal.asset,
            'timestamp': signal.timestamp.isoformat(),
            'signal_direction': signal.signal_direction,
            'signal_strength': signal.signal_strength,
            'confidence': signal.confidence,
            'risk_level': signal.risk_level,
            'position_size': signal.position_size,
            'entry_price': signal.entry_price,
            'target_price': signal.target_price,
            'stop_loss': signal.stop_loss,
            'max_hold_time': signal.max_hold_time,

            # LSTM predictions
            'lstm_predictions': {
                timeframe: {
                    'predicted_change': pred.predicted_change,
                    'confidence': pred.confidence
                } if pred else None
                for timeframe, pred in signal.lstm_predictions.items()
            },

            # Ensemble prediction
            'ensemble_prediction': {
                'predicted_change': signal.ensemble_prediction.predicted_change,
                'confidence': signal.ensemble_prediction.confidence
            } if signal.ensemble_prediction else None,

            # AI analysis
            'sentiment_analysis': {
                'sentiment_score': signal.sentiment_analysis.sentiment_score,
                'confidence': signal.sentiment_analysis.confidence,
                'market_outlook': signal.sentiment_analysis.market_outlook,
                'reasoning': signal.sentiment_analysis.reasoning
            } if signal.sentiment_analysis else None,

            'market_intelligence': {
                'prediction_1m': signal.market_intelligence.prediction_1m,
                'prediction_5m': signal.market_intelligence.prediction_5m,
                'prediction_15m': signal.market_intelligence.prediction_15m,
                'confidence': signal.market_intelligence.confidence,
                'entry_recommendation': signal.market_intelligence.entry_recommendation
            } if signal.market_intelligence else None,

            # Metadata
            'processing_time': signal.processing_time,
            'model_versions': signal.model_versions
        }

    def get_signal_stats(self) -> Dict[str, Any]:
        """Get signal generation statistics"""

        return {
            'inference_engine_metrics': inference_engine.get_performance_metrics(),
            'configuration': {
                'min_confidence': self.min_confidence,
                'max_signals_per_cycle': self.max_signals_per_cycle,
                'min_liquidity': self.min_liquidity,
                'min_volume_multiplier': self.min_volume_multiplier
            }
        }
