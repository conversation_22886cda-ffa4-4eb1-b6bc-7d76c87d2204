"""
Trade-specific logging functionality
Handles signal logging, trade execution logging, and CSV export
"""

import csv
import json
import os
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path
import structlog

from config.settings import Config


class TradeLogger:
    """Handles all trade-related logging"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = structlog.get_logger("trade_logger")
        
        # Ensure log directories exist
        self._ensure_log_directories()
        
        # Initialize CSV files
        self._init_csv_files()
    
    def _ensure_log_directories(self):
        """Create log directories if they don't exist"""
        log_paths = [
            self.config.TRADE_LOG_PATH,
            "logs/signals.json",
            "logs/performance.json"
        ]
        
        for log_path in log_paths:
            Path(log_path).parent.mkdir(parents=True, exist_ok=True)
    
    def _init_csv_files(self):
        """Initialize CSV files with headers if they don't exist"""
        # Trade history CSV
        if not os.path.exists(self.config.TRADE_LOG_PATH):
            with open(self.config.TRADE_LOG_PATH, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow([
                    'timestamp', 'asset', 'action', 'entry_price', 'exit_price',
                    'quantity', 'pnl_percent', 'pnl_usd', 'hold_time_seconds',
                    'confidence', 'prediction_1m', 'prediction_5m', 'prediction_15m',
                    'slippage', 'gas_cost_usd', 'exit_reason', 'status'
                ])
    
    async def log_signal(self, signal: Dict[str, Any]) -> None:
        """Log a generated trading signal"""
        try:
            # Structure the signal data
            signal_data = {
                'timestamp': signal.get('timestamp', datetime.utcnow().isoformat()),
                'asset': signal.get('asset'),
                'action': signal.get('action'),
                'entry_price': signal.get('entry_price'),
                'confidence': signal.get('confidence'),
                'predictions': signal.get('predictions', {}),
                'volume_analysis': signal.get('volume_analysis', {}),
                'sentiment_score': signal.get('sentiment_score'),
                'liquidity_info': signal.get('liquidity_info', {}),
                'signal_strength': signal.get('signal_strength')
            }
            
            # Log to structured logger
            self.logger.info(
                "signal_generated",
                **signal_data
            )
            
            # Also save to JSON file for detailed analysis
            await self._append_to_json_log("logs/signals.json", signal_data)
            
        except Exception as e:
            self.logger.error("failed_to_log_signal", error=str(e), signal=signal)
    
    async def log_trade(self, trade: Dict[str, Any]) -> None:
        """Log a complete trade (entry + exit)"""
        try:
            # Log to structured logger
            self.logger.info(
                "trade_completed",
                asset=trade.get('asset'),
                action=trade.get('action'),
                entry_price=trade.get('entry_price'),
                exit_price=trade.get('exit_price'),
                pnl_percent=trade.get('pnl_percent'),
                pnl_usd=trade.get('pnl_usd'),
                hold_time_seconds=trade.get('hold_time_seconds'),
                exit_reason=trade.get('exit_reason'),
                timestamp=trade.get('timestamp')
            )
            
            # Log to CSV for backtesting
            await self._log_trade_to_csv(trade)
            
        except Exception as e:
            self.logger.error("failed_to_log_trade", error=str(e), trade=trade)
    
    async def _log_trade_to_csv(self, trade: Dict[str, Any]) -> None:
        """Append trade data to CSV file"""
        try:
            with open(self.config.TRADE_LOG_PATH, 'a', newline='') as f:
                writer = csv.writer(f)
                writer.writerow([
                    trade.get('timestamp', ''),
                    trade.get('asset', ''),
                    trade.get('action', ''),
                    trade.get('entry_price', ''),
                    trade.get('exit_price', ''),
                    trade.get('quantity', ''),
                    trade.get('pnl_percent', ''),
                    trade.get('pnl_usd', ''),
                    trade.get('hold_time_seconds', ''),
                    trade.get('confidence', ''),
                    trade.get('prediction_1m', ''),
                    trade.get('prediction_5m', ''),
                    trade.get('prediction_15m', ''),
                    trade.get('slippage', ''),
                    trade.get('gas_cost_usd', ''),
                    trade.get('exit_reason', ''),
                    trade.get('status', '')
                ])
        except Exception as e:
            self.logger.error("failed_to_write_csv", error=str(e))
    
    async def _append_to_json_log(self, filepath: str, data: Dict[str, Any]) -> None:
        """Append data to JSON log file"""
        try:
            # Read existing data
            if os.path.exists(filepath):
                with open(filepath, 'r') as f:
                    try:
                        existing_data = json.load(f)
                        if not isinstance(existing_data, list):
                            existing_data = []
                    except json.JSONDecodeError:
                        existing_data = []
            else:
                existing_data = []
            
            # Append new data
            existing_data.append(data)
            
            # Keep only last 1000 entries to prevent file from growing too large
            if len(existing_data) > 1000:
                existing_data = existing_data[-1000:]
            
            # Write back to file
            with open(filepath, 'w') as f:
                json.dump(existing_data, f, indent=2, default=str)
                
        except Exception as e:
            self.logger.error("failed_to_append_json", error=str(e), filepath=filepath)
    
    async def log_error(self, error_type: str, error_message: str, context: Optional[Dict[str, Any]] = None) -> None:
        """Log an error with context"""
        self.logger.error(
            error_type,
            error=error_message,
            context=context or {},
            timestamp=datetime.utcnow().isoformat()
        )
    
    async def log_performance_metrics(self, metrics: Dict[str, Any]) -> None:
        """Log performance metrics"""
        try:
            metrics_data = {
                'timestamp': datetime.utcnow().isoformat(),
                **metrics
            }
            
            self.logger.info("performance_metrics", **metrics_data)
            
            # Also save to JSON file
            await self._append_to_json_log("logs/performance.json", metrics_data)
            
        except Exception as e:
            self.logger.error("failed_to_log_metrics", error=str(e))
    
    async def get_trade_history(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get trade history from CSV file"""
        try:
            trades = []
            if os.path.exists(self.config.TRADE_LOG_PATH):
                with open(self.config.TRADE_LOG_PATH, 'r') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        trades.append(row)
            
            if limit:
                trades = trades[-limit:]
            
            return trades
            
        except Exception as e:
            self.logger.error("failed_to_read_trade_history", error=str(e))
            return []
    
    async def get_daily_stats(self, date: str) -> Dict[str, Any]:
        """Get trading statistics for a specific date"""
        try:
            trades = await self.get_trade_history()
            daily_trades = [t for t in trades if t.get('timestamp', '').startswith(date)]
            
            if not daily_trades:
                return {}
            
            total_trades = len(daily_trades)
            winning_trades = len([t for t in daily_trades if float(t.get('pnl_percent', 0)) > 0])
            losing_trades = total_trades - winning_trades
            
            total_pnl = sum(float(t.get('pnl_percent', 0)) for t in daily_trades)
            avg_hold_time = sum(float(t.get('hold_time_seconds', 0)) for t in daily_trades) / total_trades if total_trades > 0 else 0
            
            return {
                'date': date,
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': winning_trades / total_trades if total_trades > 0 else 0,
                'total_pnl_percent': total_pnl,
                'avg_hold_time_seconds': avg_hold_time,
                'avg_pnl_per_trade': total_pnl / total_trades if total_trades > 0 else 0
            }
            
        except Exception as e:
            self.logger.error("failed_to_calculate_daily_stats", error=str(e))
            return {}
