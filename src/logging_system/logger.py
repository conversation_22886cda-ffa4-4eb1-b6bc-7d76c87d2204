"""
Structured logging system for the Polygon MATIC Signal Bot
Provides JSON formatting, file rotation, and different log levels
"""

import logging
import logging.handlers
import json
import os
from datetime import datetime
from typing import Dict, Any, Optional
import structlog
from pathlib import Path

from config.settings import Config


class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging"""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON"""
        log_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add extra fields if present
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        return json.dumps(log_entry, default=str)


class SignalBotLogger:
    """Main logger class for the Signal Bot"""
    
    def __init__(self, config: Config):
        self.config = config
        self._setup_logging()
    
    def _setup_logging(self):
        """Set up logging configuration"""
        # Create logs directory if it doesn't exist
        log_dir = Path(self.config.LOG_FILE_PATH).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Configure structlog
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.processors.JSONRenderer()
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
        
        # Set up root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, self.config.LOG_LEVEL))
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # File handler with rotation
        file_handler = logging.handlers.RotatingFileHandler(
            self.config.LOG_FILE_PATH,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        file_handler.setFormatter(JSONFormatter())
        file_handler.setLevel(getattr(logging, self.config.LOG_LEVEL))
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        console_handler.setLevel(logging.INFO)
        
        # Add handlers
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)
    
    def get_logger(self, name: str) -> structlog.BoundLogger:
        """Get a structured logger instance"""
        return structlog.get_logger(name)


class TradeLogger:
    """Specialized logger for trade signals and execution"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = structlog.get_logger("trade_logger")
        
        # Create trade logs directory
        trade_log_dir = Path(self.config.TRADE_LOG_PATH).parent
        trade_log_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize CSV file if it doesn't exist
        self._init_csv_log()
    
    def _init_csv_log(self):
        """Initialize CSV log file with headers"""
        if not os.path.exists(self.config.TRADE_LOG_PATH):
            with open(self.config.TRADE_LOG_PATH, 'w') as f:
                f.write("timestamp,asset,action,entry_price,exit_price,pnl_percent,hold_time_seconds,confidence,prediction_1m,prediction_5m,prediction_15m,slippage,gas_cost,status\n")
    
    async def log_signal(self, signal: Dict[str, Any]):
        """Log a trading signal"""
        self.logger.info(
            "signal_generated",
            asset=signal.get('asset'),
            action=signal.get('action'),
            entry_price=signal.get('entry_price'),
            confidence=signal.get('confidence'),
            predictions=signal.get('predictions', {}),
            timestamp=signal.get('timestamp')
        )
    
    async def log_trade_execution(self, trade: Dict[str, Any]):
        """Log trade execution details"""
        self.logger.info(
            "trade_executed",
            asset=trade.get('asset'),
            action=trade.get('action'),
            entry_price=trade.get('entry_price'),
            quantity=trade.get('quantity'),
            slippage=trade.get('slippage'),
            gas_cost=trade.get('gas_cost'),
            timestamp=trade.get('timestamp')
        )
    
    async def log_trade_exit(self, trade: Dict[str, Any]):
        """Log trade exit details"""
        self.logger.info(
            "trade_exited",
            asset=trade.get('asset'),
            exit_price=trade.get('exit_price'),
            pnl_percent=trade.get('pnl_percent'),
            hold_time_seconds=trade.get('hold_time_seconds'),
            exit_reason=trade.get('exit_reason'),
            timestamp=trade.get('timestamp')
        )
        
        # Also log to CSV for backtesting
        await self._log_to_csv(trade)
    
    async def _log_to_csv(self, trade: Dict[str, Any]):
        """Log trade to CSV file"""
        try:
            with open(self.config.TRADE_LOG_PATH, 'a') as f:
                csv_line = f"{trade.get('timestamp', '')},{trade.get('asset', '')},{trade.get('action', '')},{trade.get('entry_price', '')},{trade.get('exit_price', '')},{trade.get('pnl_percent', '')},{trade.get('hold_time_seconds', '')},{trade.get('confidence', '')},{trade.get('prediction_1m', '')},{trade.get('prediction_5m', '')},{trade.get('prediction_15m', '')},{trade.get('slippage', '')},{trade.get('gas_cost', '')},{trade.get('status', '')}\n"
                f.write(csv_line)
        except Exception as e:
            self.logger.error("failed_to_log_csv", error=str(e))
    
    async def log_error(self, error: str, context: Optional[Dict[str, Any]] = None):
        """Log error with context"""
        self.logger.error(
            "error_occurred",
            error=error,
            context=context or {}
        )
    
    async def log_performance_metrics(self, metrics: Dict[str, Any]):
        """Log performance metrics"""
        self.logger.info(
            "performance_metrics",
            **metrics
        )


class PerformanceLogger:
    """Logger for performance metrics and analytics"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = structlog.get_logger("performance_logger")
    
    async def log_daily_summary(self, summary: Dict[str, Any]):
        """Log daily performance summary"""
        self.logger.info(
            "daily_summary",
            date=summary.get('date'),
            total_trades=summary.get('total_trades'),
            winning_trades=summary.get('winning_trades'),
            losing_trades=summary.get('losing_trades'),
            win_rate=summary.get('win_rate'),
            total_pnl=summary.get('total_pnl'),
            avg_hold_time=summary.get('avg_hold_time'),
            max_drawdown=summary.get('max_drawdown')
        )
    
    async def log_model_performance(self, model_metrics: Dict[str, Any]):
        """Log ML model performance metrics"""
        self.logger.info(
            "model_performance",
            model_type=model_metrics.get('model_type'),
            accuracy=model_metrics.get('accuracy'),
            precision=model_metrics.get('precision'),
            recall=model_metrics.get('recall'),
            f1_score=model_metrics.get('f1_score'),
            training_time=model_metrics.get('training_time'),
            timestamp=model_metrics.get('timestamp')
        )
    
    async def log_system_metrics(self, system_metrics: Dict[str, Any]):
        """Log system performance metrics"""
        self.logger.info(
            "system_metrics",
            cpu_usage=system_metrics.get('cpu_usage'),
            memory_usage=system_metrics.get('memory_usage'),
            disk_usage=system_metrics.get('disk_usage'),
            network_latency=system_metrics.get('network_latency'),
            api_response_times=system_metrics.get('api_response_times'),
            timestamp=system_metrics.get('timestamp')
        )
