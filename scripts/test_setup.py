#!/usr/bin/env python3
"""
Test script to verify Phase 1 setup is working correctly
"""

import sys
import os
import asyncio
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from config.settings import Config
from src.logging_system.logger import SignalBotLogger, TradeLogger
from src.logging_system.trade_logger import TradeLogger as TradeLoggerV2


async def test_configuration():
    """Test configuration loading"""
    print("Testing configuration...")
    try:
        config = Config()
        print(f"✓ Configuration loaded successfully")
        print(f"  - Virtual Capital: ${config.VIRTUAL_CAPITAL}")
        print(f"  - Scan Interval: {config.SCAN_INTERVAL}s")
        print(f"  - Log Level: {config.LOG_LEVEL}")
        print(f"  - Timeframes: {config.TIMEFRAMES}")
        return True
    except Exception as e:
        print(f"✗ Configuration failed: {e}")
        return False


async def test_logging():
    """Test logging system"""
    print("\nTesting logging system...")
    try:
        config = Config()
        
        # Test main logger
        logger_system = SignalBotLogger(config)
        logger = logger_system.get_logger("test")
        logger.info("Test log message", test_field="test_value")
        
        # Test trade logger
        trade_logger = TradeLoggerV2(config)
        
        # Test signal logging
        test_signal = {
            'asset': 'MATIC/USDC',
            'action': 'BUY',
            'entry_price': 0.742,
            'confidence': 0.85,
            'predictions': {'1m': 0.4, '5m': 1.2, '15m': 0.8}
        }
        await trade_logger.log_signal(test_signal)
        
        # Test trade logging
        test_trade = {
            'asset': 'MATIC/USDC',
            'action': 'BUY',
            'entry_price': 0.742,
            'exit_price': 0.750,
            'pnl_percent': 1.08,
            'hold_time_seconds': 167,
            'exit_reason': 'TARGET_REACHED'
        }
        await trade_logger.log_trade(test_trade)
        
        print("✓ Logging system working correctly")
        print(f"  - Log file: {config.LOG_FILE_PATH}")
        print(f"  - Trade log: {config.TRADE_LOG_PATH}")
        return True
        
    except Exception as e:
        print(f"✗ Logging system failed: {e}")
        return False


async def test_imports():
    """Test that all required modules can be imported"""
    print("\nTesting imports...")
    try:
        # Test core dependencies
        import pandas as pd
        import numpy as np
        import web3
        import tensorflow as tf
        import sklearn
        import requests
        import aiohttp
        import structlog
        
        print("✓ All core dependencies imported successfully")
        print(f"  - Pandas: {pd.__version__}")
        print(f"  - NumPy: {np.__version__}")
        print(f"  - Web3: {web3.__version__}")
        print(f"  - TensorFlow: {tf.__version__}")
        print(f"  - Scikit-learn: {sklearn.__version__}")
        
        return True
        
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False


async def test_directory_structure():
    """Test that all required directories exist"""
    print("\nTesting directory structure...")
    try:
        required_dirs = [
            'config',
            'src/data_collectors',
            'src/ml_models',
            'src/signal_engine',
            'src/paper_trading',
            'src/logging_system',
            'tests/unit',
            'tests/integration',
            'logs',
            'data/cache',
            'data/models',
            'data/historical',
            'scripts',
            'docs'
        ]
        
        missing_dirs = []
        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                missing_dirs.append(dir_path)
        
        if missing_dirs:
            print(f"✗ Missing directories: {missing_dirs}")
            return False
        else:
            print("✓ All required directories exist")
            return True
            
    except Exception as e:
        print(f"✗ Directory structure test failed: {e}")
        return False


async def test_file_creation():
    """Test that log files can be created"""
    print("\nTesting file creation...")
    try:
        config = Config()
        
        # Test that we can create log files
        test_files = [
            config.LOG_FILE_PATH,
            config.TRADE_LOG_PATH,
            'logs/signals.json',
            'logs/performance.json'
        ]
        
        for file_path in test_files:
            # Create parent directory if needed
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            # Test write access
            with open(file_path, 'a') as f:
                f.write("")  # Just test we can write
        
        print("✓ All log files can be created")
        return True
        
    except Exception as e:
        print(f"✗ File creation failed: {e}")
        return False


async def main():
    """Run all tests"""
    print("=== Phase 1 Setup Verification ===\n")
    
    tests = [
        test_directory_structure,
        test_imports,
        test_configuration,
        test_file_creation,
        test_logging
    ]
    
    results = []
    for test in tests:
        result = await test()
        results.append(result)
    
    print(f"\n=== Results ===")
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✓ All {total} tests passed! Phase 1 setup is complete.")
        return 0
    else:
        print(f"✗ {total - passed} out of {total} tests failed.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
