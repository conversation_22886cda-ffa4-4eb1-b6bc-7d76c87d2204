#!/usr/bin/env python3
"""
Phase 3 Testing Suite - AI/ML Components
Comprehensive tests for LSTM models, DeepSeek integration, and inference engine
"""

import asyncio
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import config
from src.ml_models.deepseek_client import DeepSeekClient
from src.ml_models.feature_engineering import FeatureEngineer
from src.ml_models.lstm_predictor import MultiTimeframeLSTM
from src.ml_models.inference_engine import inference_engine
from src.ml_models.model_trainer import model_trainer


class Phase3Tester:
    """Comprehensive Phase 3 testing suite"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.test_results = {}
        
        # Set up logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    def generate_sample_data(self, days: int = 7) -> pd.DataFrame:
        """Generate sample OHLCV data for testing"""
        
        # Generate realistic price data
        np.random.seed(42)  # For reproducible tests
        
        periods = days * 24 * 60  # 1-minute intervals
        timestamps = pd.date_range(
            start=datetime.now() - timedelta(days=days),
            periods=periods,
            freq='1min'
        )
        
        # Generate price series with some trend and volatility
        base_price = 0.75  # MATIC price around $0.75
        returns = np.random.normal(0, 0.002, periods)  # 0.2% volatility per minute
        
        # Add some trend and mean reversion
        trend = np.linspace(0, 0.1, periods)  # 10% upward trend over period
        mean_reversion = -0.1 * np.cumsum(returns)  # Mean reversion component
        
        price_changes = returns + trend / periods + mean_reversion / periods
        prices = base_price * np.exp(np.cumsum(price_changes))
        
        # Generate OHLCV data
        data = []
        for i in range(periods):
            price = prices[i]
            volatility = abs(returns[i]) * price
            
            high = price + volatility * np.random.uniform(0, 1)
            low = price - volatility * np.random.uniform(0, 1)
            open_price = prices[i-1] if i > 0 else price
            close = price
            volume = np.random.uniform(10000, 100000)
            
            data.append({
                'timestamp': timestamps[i],
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        
        return df
    
    async def test_deepseek_client(self) -> dict:
        """Test DeepSeek API client"""
        
        self.logger.info("Testing DeepSeek client...")
        
        test_result = {
            'component': 'DeepSeek Client',
            'tests': {},
            'overall_success': False
        }
        
        try:
            async with DeepSeekClient() as client:
                # Test sentiment analysis
                market_data = {
                    'price_change_24h': 2.5,
                    'volume_change': 1.8,
                    'liquidity_usd': 1500000
                }
                
                sentiment = await client.analyze_sentiment("MATIC/USDC", market_data)
                
                test_result['tests']['sentiment_analysis'] = {
                    'success': sentiment is not None,
                    'result': {
                        'sentiment_score': sentiment.sentiment_score if sentiment else None,
                        'confidence': sentiment.confidence if sentiment else None,
                        'market_outlook': sentiment.market_outlook if sentiment else None
                    } if sentiment else None
                }
                
                # Test market intelligence
                technical_data = {
                    'rsi': 65.0,
                    'macd': 0.002,
                    'bollinger_position': 0.7,
                    'volume_ratio': 1.5
                }
                
                intelligence = await client.get_market_intelligence("MATIC/USDC", technical_data)
                
                test_result['tests']['market_intelligence'] = {
                    'success': intelligence is not None,
                    'result': {
                        'prediction_5m': intelligence.prediction_5m if intelligence else None,
                        'confidence': intelligence.confidence if intelligence else None,
                        'entry_recommendation': intelligence.entry_recommendation if intelligence else None
                    } if intelligence else None
                }
                
                # Test batch analysis
                assets = ["MATIC/USDC", "WETH/MATIC"]
                batch_data = {
                    "MATIC/USDC": market_data,
                    "WETH/MATIC": market_data
                }
                
                batch_results = await client.batch_analyze(assets, batch_data)
                
                test_result['tests']['batch_analysis'] = {
                    'success': len(batch_results) == len(assets),
                    'assets_processed': len(batch_results)
                }
                
                # Overall success
                test_result['overall_success'] = all(
                    test['success'] for test in test_result['tests'].values()
                )
                
        except Exception as e:
            test_result['error'] = str(e)
            self.logger.error(f"DeepSeek client test failed: {e}")
        
        return test_result
    
    def test_feature_engineering(self) -> dict:
        """Test feature engineering pipeline"""
        
        self.logger.info("Testing feature engineering...")
        
        test_result = {
            'component': 'Feature Engineering',
            'tests': {},
            'overall_success': False
        }
        
        try:
            engineer = FeatureEngineer()
            sample_data = self.generate_sample_data(days=3)
            
            # Test feature creation
            feature_set = engineer.create_features(sample_data)
            
            test_result['tests']['feature_creation'] = {
                'success': feature_set is not None and len(feature_set.features) > 0,
                'feature_count': len(feature_set.feature_names) if feature_set else 0,
                'data_points': len(feature_set.features) if feature_set else 0,
                'feature_names': feature_set.feature_names[:10] if feature_set else []  # First 10 features
            }
            
            # Test sequence creation
            if feature_set:
                X, y = engineer.create_sequences(feature_set)
                
                test_result['tests']['sequence_creation'] = {
                    'success': X is not None and len(X) > 0,
                    'sequence_count': len(X) if X is not None else 0,
                    'sequence_length': X.shape[1] if X is not None else 0,
                    'feature_count': X.shape[2] if X is not None else 0
                }
            
            # Test normalization
            if feature_set:
                normalized, scaler = engineer.normalize_features(feature_set.features)
                
                test_result['tests']['normalization'] = {
                    'success': normalized is not None and scaler is not None,
                    'normalized_shape': normalized.shape if normalized is not None else None
                }
            
            test_result['overall_success'] = all(
                test['success'] for test in test_result['tests'].values()
            )
            
        except Exception as e:
            test_result['error'] = str(e)
            self.logger.error(f"Feature engineering test failed: {e}")
        
        return test_result
    
    def test_lstm_predictor(self) -> dict:
        """Test LSTM predictor models"""
        
        self.logger.info("Testing LSTM predictor...")
        
        test_result = {
            'component': 'LSTM Predictor',
            'tests': {},
            'overall_success': False
        }
        
        try:
            multi_lstm = MultiTimeframeLSTM()
            sample_data = self.generate_sample_data(days=5)
            
            # Test model loading
            load_results = multi_lstm.load_all_models()
            
            test_result['tests']['model_loading'] = {
                'success': any(load_results.values()),
                'loaded_models': {tf: success for tf, success in load_results.items()}
            }
            
            # Test prediction (if models are loaded)
            if any(load_results.values()):
                predictions = multi_lstm.predict_all_timeframes(sample_data, "MATIC/USDC")
                
                test_result['tests']['prediction'] = {
                    'success': any(pred is not None for pred in predictions.values()),
                    'predictions': {
                        tf: {
                            'predicted_change': pred.predicted_change,
                            'confidence': pred.confidence
                        } if pred else None
                        for tf, pred in predictions.items()
                    }
                }
                
                # Test ensemble prediction
                ensemble = multi_lstm.get_ensemble_prediction(sample_data, "MATIC/USDC")
                
                test_result['tests']['ensemble_prediction'] = {
                    'success': ensemble is not None,
                    'result': {
                        'predicted_change': ensemble.predicted_change,
                        'confidence': ensemble.confidence
                    } if ensemble else None
                }
            else:
                test_result['tests']['prediction'] = {
                    'success': False,
                    'reason': 'No models loaded'
                }
                test_result['tests']['ensemble_prediction'] = {
                    'success': False,
                    'reason': 'No models loaded'
                }
            
            # Test model info
            for timeframe in config.TIMEFRAMES:
                model = multi_lstm.models[timeframe]
                info = model.get_model_info()
                
                test_result['tests'][f'{timeframe}_model_info'] = {
                    'success': True,
                    'info': info
                }
            
            test_result['overall_success'] = any(
                test.get('success', False) for test in test_result['tests'].values()
            )
            
        except Exception as e:
            test_result['error'] = str(e)
            self.logger.error(f"LSTM predictor test failed: {e}")
        
        return test_result
    
    async def test_inference_engine(self) -> dict:
        """Test inference engine"""
        
        self.logger.info("Testing inference engine...")
        
        test_result = {
            'component': 'Inference Engine',
            'tests': {},
            'overall_success': False
        }
        
        try:
            sample_data = self.generate_sample_data(days=2)
            
            # Test signal generation
            signal = await inference_engine.generate_signal("MATIC/USDC", sample_data)
            
            test_result['tests']['signal_generation'] = {
                'success': signal is not None,
                'result': {
                    'signal_direction': signal.signal_direction,
                    'signal_strength': signal.signal_strength,
                    'confidence': signal.confidence,
                    'processing_time': signal.processing_time
                } if signal else None
            }
            
            # Test batch signal generation
            assets = ["MATIC/USDC", "WETH/MATIC"]
            batch_signals = await inference_engine.batch_generate_signals(assets)
            
            test_result['tests']['batch_signals'] = {
                'success': len(batch_signals) == len(assets),
                'results': {
                    asset: signal is not None for asset, signal in batch_signals.items()
                }
            }
            
            # Test performance metrics
            metrics = inference_engine.get_performance_metrics()
            
            test_result['tests']['performance_metrics'] = {
                'success': metrics is not None,
                'metrics': metrics
            }
            
            test_result['overall_success'] = any(
                test.get('success', False) for test in test_result['tests'].values()
            )
            
        except Exception as e:
            test_result['error'] = str(e)
            self.logger.error(f"Inference engine test failed: {e}")
        
        return test_result
    
    def test_model_trainer(self) -> dict:
        """Test model trainer (basic functionality)"""
        
        self.logger.info("Testing model trainer...")
        
        test_result = {
            'component': 'Model Trainer',
            'tests': {},
            'overall_success': False
        }
        
        try:
            # Test training summary
            summary = model_trainer.get_training_summary()
            
            test_result['tests']['training_summary'] = {
                'success': summary is not None,
                'summary': summary
            }
            
            # Test configuration
            test_result['tests']['configuration'] = {
                'success': True,
                'retrain_interval': model_trainer.retrain_interval_hours,
                'min_data_points': model_trainer.min_data_points
            }
            
            test_result['overall_success'] = all(
                test.get('success', False) for test in test_result['tests'].values()
            )
            
        except Exception as e:
            test_result['error'] = str(e)
            self.logger.error(f"Model trainer test failed: {e}")
        
        return test_result
    
    async def run_all_tests(self) -> dict:
        """Run all Phase 3 tests"""
        
        self.logger.info("Starting Phase 3 comprehensive test suite...")
        
        # Run all tests
        tests = [
            ('deepseek_client', self.test_deepseek_client()),
            ('feature_engineering', self.test_feature_engineering()),
            ('lstm_predictor', self.test_lstm_predictor()),
            ('inference_engine', self.test_inference_engine()),
            ('model_trainer', self.test_model_trainer())
        ]
        
        results = {}
        
        for test_name, test_coro in tests:
            try:
                if asyncio.iscoroutine(test_coro):
                    result = await test_coro
                else:
                    result = test_coro
                
                results[test_name] = result
                
                status = "✓ PASS" if result['overall_success'] else "✗ FAIL"
                self.logger.info(f"{test_name}: {status}")
                
            except Exception as e:
                results[test_name] = {
                    'component': test_name,
                    'overall_success': False,
                    'error': str(e)
                }
                self.logger.error(f"{test_name}: ✗ ERROR - {e}")
        
        # Calculate overall results
        total_tests = len(results)
        passed_tests = sum(1 for r in results.values() if r['overall_success'])
        
        overall_result = {
            'phase': 'Phase 3 - AI/ML Components',
            'timestamp': datetime.now().isoformat(),
            'total_components': total_tests,
            'passed_components': passed_tests,
            'success_rate': passed_tests / total_tests if total_tests > 0 else 0,
            'overall_success': passed_tests >= total_tests * 0.6,  # 60% pass rate
            'detailed_results': results
        }
        
        return overall_result
    
    def print_summary(self, results: dict):
        """Print test summary"""
        
        print("\n" + "="*60)
        print(f"PHASE 3 TEST SUMMARY - {results['phase']}")
        print("="*60)
        print(f"Timestamp: {results['timestamp']}")
        print(f"Components Tested: {results['total_components']}")
        print(f"Components Passed: {results['passed_components']}")
        print(f"Success Rate: {results['success_rate']:.1%}")
        print(f"Overall Status: {'✓ PASS' if results['overall_success'] else '✗ FAIL'}")
        print()
        
        # Component details
        for component, result in results['detailed_results'].items():
            status = "✓ PASS" if result['overall_success'] else "✗ FAIL"
            print(f"{component.replace('_', ' ').title()}: {status}")
            
            if 'error' in result:
                print(f"  Error: {result['error']}")
            else:
                for test_name, test_result in result.get('tests', {}).items():
                    test_status = "✓" if test_result.get('success', False) else "✗"
                    print(f"  {test_name}: {test_status}")
        
        print("\n" + "="*60)


async def main():
    """Run Phase 3 tests"""
    
    tester = Phase3Tester()
    results = await tester.run_all_tests()
    tester.print_summary(results)
    
    # Save results to file
    import json
    with open('logs/phase3_test_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\nDetailed results saved to: logs/phase3_test_results.json")
    
    # Exit with appropriate code
    exit_code = 0 if results['overall_success'] else 1
    return exit_code


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
