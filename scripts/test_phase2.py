#!/usr/bin/env python3
"""
Phase 2 Implementation Test Script
Tests all data infrastructure components
"""

import asyncio
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def test_imports():
    """Test that all Phase 2 components can be imported"""
    print("Testing Phase 2 imports...")
    
    try:
        from src.data_collectors.polygon_rpc import PolygonRPCClient
        print("✅ PolygonRPCClient imported successfully")
    except Exception as e:
        print(f"❌ PolygonRPCClient import failed: {e}")
        return False
    
    try:
        from src.data_collectors.dex_collectors import QuickSwapCollector, UniswapV3Collector
        print("✅ DEX collectors imported successfully")
    except Exception as e:
        print(f"❌ DEX collectors import failed: {e}")
        return False
    
    try:
        from src.data_collectors.coinglass_client import CoinglassClient
        print("✅ CoinglassClient imported successfully")
    except Exception as e:
        print(f"❌ CoinglassClient import failed: {e}")
        return False
    
    try:
        from src.data_collectors.cache_manager import CacheManager
        print("✅ CacheManager imported successfully")
    except Exception as e:
        print(f"❌ CacheManager import failed: {e}")
        return False
    
    try:
        from src.data_collectors.price_aggregator import PriceAggregator
        print("✅ PriceAggregator imported successfully")
    except Exception as e:
        print(f"❌ PriceAggregator import failed: {e}")
        return False
    
    try:
        from src.data_collectors.asset_selector import AssetSelector
        print("✅ AssetSelector imported successfully")
    except Exception as e:
        print(f"❌ AssetSelector import failed: {e}")
        return False
    
    try:
        from src.data_collectors.data_validator import DataValidator
        print("✅ DataValidator imported successfully")
    except Exception as e:
        print(f"❌ DataValidator import failed: {e}")
        return False
    
    return True

async def test_basic_functionality():
    """Test basic functionality of Phase 2 components"""
    print("\nTesting basic functionality...")
    
    try:
        from src.data_collectors.polygon_rpc import polygon_rpc
        from src.data_collectors.cache_manager import cache_manager
        from src.data_collectors.data_validator import data_validator
        
        # Test RPC health check
        print("Testing RPC health check...")
        rpc_health = await polygon_rpc.health_check()
        print(f"RPC Health: {rpc_health.get('healthy', False)}")
        
        # Test cache system
        print("Testing cache system...")
        cache_health = await cache_manager.health_check()
        print(f"Cache Health: Redis={cache_health.get('redis', {}).get('healthy', False)}, SQLite={cache_health.get('sqlite', {}).get('healthy', False)}")
        
        # Test cache operations
        test_data = {'test': 'data', 'timestamp': '2025-01-01T00:00:00Z'}
        await cache_manager.set('test_key', test_data, 60)
        retrieved = await cache_manager.get('test_key')
        print(f"Cache test: {'✅ PASS' if retrieved and retrieved['test'] == 'data' else '❌ FAIL'}")
        
        # Test data validator
        print("Testing data validator...")
        sample_price_data = {
            'price': 0.85,
            'timestamp': '2025-01-01T00:00:00Z',
            'sources': [{'source': 'test', 'price': 0.85}],
            'confidence': 0.8
        }
        validation = await data_validator.validate_price_data(sample_price_data)
        print(f"Data validation test: {'✅ PASS' if validation['valid'] else '❌ FAIL'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False

async def test_data_collectors():
    """Test data collectors (with mock/limited data)"""
    print("\nTesting data collectors...")
    
    try:
        from src.data_collectors.dex_collectors import quickswap_collector, uniswap_v3_collector
        from src.data_collectors.asset_selector import asset_selector
        
        # Test QuickSwap collector (will likely fail without network, but should not crash)
        print("Testing QuickSwap collector...")
        try:
            qs_pairs = await quickswap_collector.get_top_pairs(3)
            print(f"QuickSwap: {len(qs_pairs)} pairs fetched")
        except Exception as e:
            print(f"QuickSwap test failed (expected): {str(e)[:100]}...")
        
        # Test Uniswap V3 collector
        print("Testing Uniswap V3 collector...")
        try:
            uni_pools = await uniswap_v3_collector.get_top_pools(3)
            print(f"Uniswap V3: {len(uni_pools)} pools fetched")
        except Exception as e:
            print(f"Uniswap V3 test failed (expected): {str(e)[:100]}...")
        
        # Test asset selector
        print("Testing asset selector...")
        try:
            top_assets = await asset_selector.get_top_assets()
            print(f"Asset selector: {len(top_assets)} assets selected")
        except Exception as e:
            print(f"Asset selector test failed (expected): {str(e)[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Data collectors test failed: {e}")
        return False

async def test_integration():
    """Test integration with main bot"""
    print("\nTesting integration with main bot...")
    
    try:
        # Import main bot
        from main import SignalBot
        
        # Create bot instance
        bot = SignalBot()
        print("✅ SignalBot created successfully")
        
        # Test data systems initialization
        await bot._initialize_data_systems()
        print(f"Data systems health: {bot.data_systems_healthy}")
        
        # Test market data collection
        if bot.data_systems_healthy:
            market_data = await bot._collect_market_data()
            print(f"Market data collected: {len(market_data)} fields")
        else:
            print("Skipping market data collection (systems unhealthy)")
        
        # Cleanup
        await bot._cleanup_data_systems()
        print("✅ Integration test completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

async def main():
    """Run all Phase 2 tests"""
    print("🚀 Phase 2 Implementation Test Suite")
    print("=" * 50)
    
    tests = [
        ("Import Tests", test_imports),
        ("Basic Functionality", test_basic_functionality),
        ("Data Collectors", test_data_collectors),
        ("Integration", test_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name}...")
        try:
            result = await test_func()
            results.append((test_name, result))
            print(f"{'✅ PASSED' if result else '❌ FAILED'}")
        except Exception as e:
            print(f"❌ FAILED with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All Phase 2 tests passed! Implementation is ready.")
        return True
    else:
        print("⚠️  Some tests failed. Phase 2 implementation needs attention.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
