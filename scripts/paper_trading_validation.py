#!/usr/bin/env python3
"""
Paper Trading Validation Script
Runs a short paper trading session to validate system performance
"""

import asyncio
import sys
import os
import logging
import signal
from datetime import datetime, timedelta

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import config
from main import SignalBot

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PaperTradingValidator:
    """Validates paper trading system performance"""
    
    def __init__(self, duration_minutes: int = 30):
        self.duration_minutes = duration_minutes
        self.start_time = None
        self.end_time = None
        self.bot = None
        self.running = False
        self.stats = {
            'cycles_completed': 0,
            'signals_generated': 0,
            'trades_executed': 0,
            'errors_encountered': 0,
            'start_time': None,
            'end_time': None
        }
    
    async def run_validation(self):
        """Run paper trading validation"""
        logger.info(f"🚀 Starting {self.duration_minutes}-minute paper trading validation...")
        
        try:
            # Initialize bot
            self.bot = SignalBot()
            await self.bot.initialize()
            
            self.start_time = datetime.now()
            self.stats['start_time'] = self.start_time
            self.running = True
            
            logger.info(f"✅ Bot initialized. Running until {self.start_time + timedelta(minutes=self.duration_minutes)}")
            
            # Run validation cycles
            cycle_count = 0
            while self.running and self._should_continue():
                try:
                    cycle_start = datetime.now()
                    logger.info(f"📊 Starting validation cycle {cycle_count + 1}")
                    
                    # Run one AI cycle
                    await self.bot._run_ai_powered_cycle()
                    
                    cycle_count += 1
                    self.stats['cycles_completed'] = cycle_count
                    
                    cycle_duration = (datetime.now() - cycle_start).total_seconds()
                    logger.info(f"✅ Cycle {cycle_count} completed in {cycle_duration:.1f}s")
                    
                    # Wait before next cycle (reduced for validation)
                    await asyncio.sleep(30)  # 30 seconds between cycles
                    
                except Exception as e:
                    self.stats['errors_encountered'] += 1
                    logger.error(f"❌ Error in cycle {cycle_count + 1}: {e}")
                    await asyncio.sleep(10)  # Short wait before retry
            
            self.end_time = datetime.now()
            self.stats['end_time'] = self.end_time
            
            # Generate validation report
            await self._generate_report()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Validation failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        finally:
            if self.bot:
                await self.bot.cleanup()
    
    def _should_continue(self) -> bool:
        """Check if validation should continue"""
        if not self.start_time:
            return False
        
        elapsed = datetime.now() - self.start_time
        return elapsed.total_seconds() < (self.duration_minutes * 60)
    
    async def _generate_report(self):
        """Generate validation report"""
        duration = self.end_time - self.start_time
        duration_minutes = duration.total_seconds() / 60
        
        logger.info("📋 PAPER TRADING VALIDATION REPORT")
        logger.info("=" * 50)
        logger.info(f"Duration: {duration_minutes:.1f} minutes")
        logger.info(f"Cycles completed: {self.stats['cycles_completed']}")
        logger.info(f"Signals generated: {self.stats['signals_generated']}")
        logger.info(f"Trades executed: {self.stats['trades_executed']}")
        logger.info(f"Errors encountered: {self.stats['errors_encountered']}")
        
        if self.stats['cycles_completed'] > 0:
            avg_cycle_time = duration_minutes / self.stats['cycles_completed']
            logger.info(f"Average cycle time: {avg_cycle_time:.1f} minutes")
        
        # Check system health
        if self.bot and hasattr(self.bot, 'portfolio_manager'):
            try:
                portfolio_status = await self.bot.portfolio_manager.get_portfolio_summary()
                logger.info(f"Portfolio value: ${portfolio_status.get('total_value', 0):.2f}")
                logger.info(f"Active positions: {portfolio_status.get('active_positions', 0)}")
            except Exception as e:
                logger.warning(f"Could not get portfolio status: {e}")
        
        # Validation results
        success_criteria = {
            'cycles_completed': self.stats['cycles_completed'] >= 2,
            'no_critical_errors': self.stats['errors_encountered'] < 5,
            'system_responsive': duration_minutes >= (self.duration_minutes * 0.8)
        }
        
        all_passed = all(success_criteria.values())
        
        logger.info("🎯 VALIDATION RESULTS:")
        for criterion, passed in success_criteria.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            logger.info(f"  {criterion}: {status}")
        
        if all_passed:
            logger.info("🎉 VALIDATION SUCCESSFUL - System ready for operation!")
        else:
            logger.warning("⚠️  VALIDATION ISSUES DETECTED - Review before going live")
        
        return all_passed
    
    def stop(self):
        """Stop validation"""
        logger.info("🛑 Stopping validation...")
        self.running = False


async def main():
    """Main entry point"""
    # Set up signal handlers
    validator = PaperTradingValidator(duration_minutes=10)  # 10-minute validation
    
    def signal_handler(signum, frame):
        logger.info("Received interrupt signal")
        validator.stop()
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        success = await validator.run_validation()
        return 0 if success else 1
    except KeyboardInterrupt:
        logger.info("Validation interrupted by user")
        validator.stop()
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
