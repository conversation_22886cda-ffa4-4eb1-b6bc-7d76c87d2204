#!/usr/bin/env python3
"""
Core ML Components Test - Phase 3
Tests only the core ML components without dependencies
"""

import asyncio
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_feature_engineering():
    """Test feature engineering without external dependencies"""
    
    print("Testing Feature Engineering...")
    
    try:
        # Import only what we need
        from src.ml_models.feature_engineering import FeatureEngineer, TechnicalIndicators
        
        # Generate sample data
        np.random.seed(42)
        periods = 1000
        timestamps = pd.date_range(start=datetime.now() - timedelta(days=1), periods=periods, freq='1min')
        
        base_price = 0.75
        returns = np.random.normal(0, 0.002, periods)
        prices = base_price * np.exp(np.cumsum(returns))
        
        data = []
        for i in range(periods):
            price = prices[i]
            volatility = abs(returns[i]) * price
            
            high = price + volatility * np.random.uniform(0, 1)
            low = price - volatility * np.random.uniform(0, 1)
            open_price = prices[i-1] if i > 0 else price
            close = price
            volume = np.random.uniform(10000, 100000)
            
            data.append({
                'timestamp': timestamps[i],
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        
        # Test technical indicators
        ti = TechnicalIndicators()
        
        # Test RSI
        rsi = ti.rsi(df['close'].values)
        print(f"  RSI: ✓ (last value: {rsi[-1]:.2f})")
        
        # Test MACD
        macd, signal, hist = ti.macd(df['close'].values)
        print(f"  MACD: ✓ (last value: {macd[-1]:.6f})")
        
        # Test Bollinger Bands
        upper, middle, lower = ti.bollinger_bands(df['close'].values)
        print(f"  Bollinger Bands: ✓ (last middle: {middle[-1]:.6f})")
        
        # Test feature engineering
        engineer = FeatureEngineer()
        feature_set = engineer.create_features(df)
        
        print(f"  Feature Engineering: ✓ ({len(feature_set.feature_names)} features, {len(feature_set.features)} data points)")
        print(f"  Sample features: {feature_set.feature_names[:5]}")
        
        # Test sequences
        X, y = engineer.create_sequences(feature_set)
        print(f"  Sequences: ✓ ({X.shape[0]} sequences, {X.shape[1]} timesteps, {X.shape[2]} features)")
        
        return True
        
    except Exception as e:
        print(f"  Feature Engineering: ✗ Error - {e}")
        return False


async def test_deepseek_client():
    """Test DeepSeek client"""
    
    print("Testing DeepSeek Client...")
    
    try:
        from src.ml_models.deepseek_client import DeepSeekClient
        
        async with DeepSeekClient() as client:
            # Test sentiment analysis
            market_data = {
                'price_change_24h': 2.5,
                'volume_change': 1.8,
                'liquidity_usd': 1500000
            }
            
            sentiment = await client.analyze_sentiment("MATIC/USDC", market_data)
            
            if sentiment:
                print(f"  Sentiment Analysis: ✓ (score: {sentiment.sentiment_score:.3f}, outlook: {sentiment.market_outlook})")
            else:
                print(f"  Sentiment Analysis: ✗ No response")
                return False
            
            # Test market intelligence
            technical_data = {
                'rsi': 65.0,
                'macd': 0.002,
                'bollinger_position': 0.7,
                'volume_ratio': 1.5
            }
            
            intelligence = await client.get_market_intelligence("MATIC/USDC", technical_data)
            
            if intelligence:
                print(f"  Market Intelligence: ✓ (5m prediction: {intelligence.prediction_5m:.3f}%, recommendation: {intelligence.entry_recommendation})")
            else:
                print(f"  Market Intelligence: ✗ No response")
                return False
            
            return True
            
    except Exception as e:
        print(f"  DeepSeek Client: ✗ Error - {e}")
        return False


def test_lstm_models():
    """Test LSTM models (basic structure)"""
    
    print("Testing LSTM Models...")
    
    try:
        from src.ml_models.lstm_predictor import LSTMPredictor, MultiTimeframeLSTM
        
        # Test model initialization
        lstm_5m = LSTMPredictor("5m")
        print(f"  LSTM 5m Model: ✓ (initialized)")
        
        # Test multi-timeframe
        multi_lstm = MultiTimeframeLSTM()
        print(f"  Multi-timeframe LSTM: ✓ (initialized with {len(multi_lstm.models)} models)")
        
        # Test model loading (will fail if no models exist, but that's expected)
        load_results = multi_lstm.load_all_models()
        loaded_count = sum(1 for success in load_results.values() if success)
        
        if loaded_count > 0:
            print(f"  Model Loading: ✓ ({loaded_count}/{len(load_results)} models loaded)")
        else:
            print(f"  Model Loading: ⚠ No pre-trained models found (run train_initial_models.py first)")
        
        # Test model info
        for timeframe in ['1m', '5m', '15m']:
            model = multi_lstm.models[timeframe]
            info = model.get_model_info()
            print(f"  {timeframe} Model Info: ✓")
        
        return True
        
    except Exception as e:
        print(f"  LSTM Models: ✗ Error - {e}")
        return False


async def main():
    """Run core ML tests"""
    
    print("="*60)
    print("PHASE 3 CORE ML COMPONENTS TEST")
    print("="*60)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print()
    
    # Set up logging
    logging.basicConfig(level=logging.WARNING)  # Suppress info logs
    
    results = []
    
    # Test 1: Feature Engineering
    results.append(test_feature_engineering())
    print()
    
    # Test 2: DeepSeek Client
    results.append(await test_deepseek_client())
    print()
    
    # Test 3: LSTM Models
    results.append(test_lstm_models())
    print()
    
    # Summary
    passed = sum(results)
    total = len(results)
    success_rate = passed / total
    
    print("="*60)
    print("TEST SUMMARY")
    print("="*60)
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {success_rate:.1%}")
    print(f"Overall Status: {'✓ PASS' if success_rate >= 0.5 else '✗ FAIL'}")
    print()
    
    if success_rate >= 0.5:
        print("🎉 Phase 3 core components are working!")
        print("Next steps:")
        print("1. Run 'python scripts/train_initial_models.py' to train LSTM models")
        print("2. Run 'python main.py' to start the AI-powered bot")
    else:
        print("❌ Some core components failed. Check the errors above.")
    
    print("="*60)
    
    return 0 if success_rate >= 0.5 else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
