#!/usr/bin/env python3
"""
Initial Model Training Script
Train LSTM models with sample data for Phase 3 demonstration
"""

import asyncio
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import config
from src.ml_models.lstm_predictor import MultiTimeframeLSTM
from src.ml_models.feature_engineering import FeatureEngineer


def generate_training_data(days: int = 30) -> pd.DataFrame:
    """Generate realistic training data for MATIC/USDC"""
    
    np.random.seed(42)  # For reproducible results
    
    periods = days * 24 * 60  # 1-minute intervals
    timestamps = pd.date_range(
        start=datetime.now() - timedelta(days=days),
        periods=periods,
        freq='1min'
    )
    
    # Generate realistic MATIC price movement
    base_price = 0.75
    
    # Create multiple components for realistic price action
    # 1. Random walk component
    random_returns = np.random.normal(0, 0.003, periods)  # 0.3% volatility
    
    # 2. Trend component (slight upward bias)
    trend = np.linspace(0, 0.2, periods)  # 20% trend over period
    
    # 3. Mean reversion component
    mean_reversion_strength = 0.05
    cumulative_deviation = np.cumsum(random_returns)
    mean_reversion = -mean_reversion_strength * cumulative_deviation
    
    # 4. Volatility clustering (GARCH-like)
    volatility = np.ones(periods) * 0.003
    for i in range(1, periods):
        volatility[i] = 0.1 * volatility[i-1] + 0.9 * abs(random_returns[i-1])
    
    # 5. Intraday patterns (higher volatility during certain hours)
    hours = np.array([ts.hour for ts in timestamps])
    intraday_multiplier = 1 + 0.3 * np.sin(2 * np.pi * hours / 24)
    
    # Combine all components
    total_returns = (
        random_returns * volatility * intraday_multiplier +
        trend / periods +
        mean_reversion / periods
    )
    
    # Generate price series
    prices = base_price * np.exp(np.cumsum(total_returns))
    
    # Generate OHLCV data
    data = []
    for i in range(periods):
        price = prices[i]
        vol = volatility[i] * price * intraday_multiplier[i]
        
        # Generate realistic OHLC
        open_price = prices[i-1] if i > 0 else price
        
        # High and low based on volatility
        high_offset = vol * np.random.uniform(0.5, 2.0)
        low_offset = vol * np.random.uniform(0.5, 2.0)
        
        high = max(open_price, price) + high_offset
        low = min(open_price, price) - low_offset
        close = price
        
        # Volume correlated with volatility and price movement
        base_volume = 50000
        volume_multiplier = 1 + 5 * vol + 2 * abs(total_returns[i])
        volume = base_volume * volume_multiplier * np.random.uniform(0.5, 1.5)
        
        data.append({
            'timestamp': timestamps[i],
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('timestamp', inplace=True)
    
    return df


async def train_models():
    """Train LSTM models for all timeframes"""
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger(__name__)
    
    logger.info("Starting initial model training...")
    
    # Generate training data
    logger.info("Generating training data...")
    training_data = generate_training_data(days=60)  # 2 months of data
    logger.info(f"Generated {len(training_data)} data points")
    
    # Initialize multi-timeframe LSTM
    multi_lstm = MultiTimeframeLSTM()
    
    # Train models for each timeframe
    results = {}
    
    for timeframe in config.TIMEFRAMES:
        logger.info(f"Training {timeframe} model...")
        
        try:
            model = multi_lstm.models[timeframe]
            
            # Train the model
            training_result = model.train(training_data, validation_split=0.2)
            
            results[timeframe] = {
                'success': True,
                'final_loss': training_result['final_loss'],
                'final_val_loss': training_result['final_val_loss'],
                'epochs_trained': training_result['epochs_trained'],
                'feature_count': training_result['feature_count']
            }
            
            logger.info(f"{timeframe} model training completed successfully")
            logger.info(f"  Final loss: {training_result['final_loss']:.6f}")
            logger.info(f"  Validation loss: {training_result['final_val_loss']:.6f}")
            logger.info(f"  Epochs: {training_result['epochs_trained']}")
            
        except Exception as e:
            logger.error(f"Failed to train {timeframe} model: {e}")
            results[timeframe] = {
                'success': False,
                'error': str(e)
            }
    
    # Test predictions
    logger.info("Testing trained models...")
    
    # Generate test data (last week)
    test_data = generate_training_data(days=7)
    
    for timeframe in config.TIMEFRAMES:
        if results[timeframe]['success']:
            try:
                model = multi_lstm.models[timeframe]
                prediction = model.predict(test_data, "MATIC/USDC")
                
                if prediction:
                    logger.info(f"{timeframe} prediction: {prediction.predicted_change:.3f}% "
                              f"(confidence: {prediction.confidence:.3f})")
                else:
                    logger.warning(f"{timeframe} prediction failed")
                    
            except Exception as e:
                logger.error(f"Failed to test {timeframe} model: {e}")
    
    # Test ensemble prediction
    try:
        ensemble_prediction = multi_lstm.get_ensemble_prediction(test_data, "MATIC/USDC")
        
        if ensemble_prediction:
            logger.info(f"Ensemble prediction: {ensemble_prediction.predicted_change:.3f}% "
                       f"(confidence: {ensemble_prediction.confidence:.3f})")
        else:
            logger.warning("Ensemble prediction failed")
            
    except Exception as e:
        logger.error(f"Failed to test ensemble prediction: {e}")
    
    # Summary
    successful_models = sum(1 for r in results.values() if r['success'])
    total_models = len(results)
    
    logger.info(f"Training completed: {successful_models}/{total_models} models trained successfully")
    
    if successful_models > 0:
        logger.info("Models are ready for inference!")
        return True
    else:
        logger.error("No models were trained successfully")
        return False


def main():
    """Main entry point"""
    
    print("MATIC Signal Bot - Initial Model Training")
    print("=" * 50)
    
    success = asyncio.run(train_models())
    
    if success:
        print("\n✓ Model training completed successfully!")
        print("You can now run the main bot with trained models.")
    else:
        print("\n✗ Model training failed!")
        print("Check the logs for details.")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
