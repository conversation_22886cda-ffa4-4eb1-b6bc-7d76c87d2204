#!/usr/bin/env python3
"""
Phase 2 Demonstration Script
Shows the data infrastructure capabilities without external dependencies
"""

import asyncio
import sys
import os
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def demo_cache_system():
    """Demonstrate caching system functionality"""
    print("🗄️  Cache System Demo")
    print("-" * 30)
    
    try:
        from src.data_collectors.cache_manager import CacheManager
        
        cache = CacheManager()
        
        # Test cache operations
        test_data = {
            'price': 0.85,
            'timestamp': datetime.now().isoformat(),
            'source': 'demo'
        }
        
        print("Setting cache data...")
        success = await cache.set('demo_key', test_data, 60)
        print(f"Cache set: {'✅ Success' if success else '❌ Failed'}")
        
        print("Retrieving cache data...")
        retrieved = await cache.get('demo_key')
        print(f"Cache get: {'✅ Success' if retrieved else '❌ Failed'}")
        
        if retrieved:
            print(f"Retrieved data: {retrieved}")
        
        # Test cache stats
        stats = await cache.get_stats()
        print(f"Cache stats: {stats}")
        
        # Test health check
        health = await cache.health_check()
        print(f"Cache health: {health}")
        
        await cache.close()
        return True
        
    except Exception as e:
        print(f"❌ Cache demo failed: {e}")
        return False

async def demo_data_validator():
    """Demonstrate data validation system"""
    print("\n🔍 Data Validator Demo")
    print("-" * 30)
    
    try:
        from src.data_collectors.data_validator import DataValidator
        
        validator = DataValidator()
        
        # Test price data validation
        sample_price_data = {
            'price': 0.85,
            'timestamp': datetime.now().isoformat(),
            'sources': [
                {'source': 'quickswap', 'price': 0.85, 'liquidity_usd': 1000000},
                {'source': 'uniswap_v3', 'price': 0.87, 'liquidity_usd': 2000000}
            ],
            'confidence': 0.8
        }
        
        print("Validating price data...")
        validation = await validator.validate_price_data(sample_price_data)
        print(f"Validation result: {'✅ Valid' if validation['valid'] else '❌ Invalid'}")
        print(f"Quality score: {validation['quality_score']:.2f}")
        
        if validation['warnings']:
            print(f"Warnings: {validation['warnings']}")
        
        # Test anomaly detection
        print("\nTesting anomaly detection...")
        historical_prices = [0.80, 0.82, 0.81, 0.83, 0.79, 0.84]
        current_price = 0.85  # Normal price
        
        anomaly = await validator.detect_price_anomalies(current_price, historical_prices)
        print(f"Anomaly detected: {'⚠️ Yes' if anomaly['is_anomaly'] else '✅ No'}")
        print(f"Z-score: {anomaly['z_score']:.2f}")
        
        # Test with anomalous price
        anomalous_price = 1.50  # Significant spike
        anomaly = await validator.detect_price_anomalies(anomalous_price, historical_prices)
        print(f"Anomaly with spike: {'⚠️ Yes' if anomaly['is_anomaly'] else '✅ No'}")
        print(f"Anomaly type: {anomaly.get('anomaly_type', 'None')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Validator demo failed: {e}")
        return False

async def demo_asset_selector():
    """Demonstrate asset selection logic"""
    print("\n🎯 Asset Selector Demo")
    print("-" * 30)
    
    try:
        from src.data_collectors.asset_selector import AssetSelector
        
        selector = AssetSelector()
        
        # Demo asset filtering
        mock_assets = [
            {
                'token0_symbol': 'MATIC',
                'token1_symbol': 'USDC',
                'token0_address': '******************************************',
                'token1_address': '******************************************',
                'liquidity_usd': 2000000,
                'volume_24h_usd': 500000,
                'tx_count': 1000
            },
            {
                'token0_symbol': 'WETH',
                'token1_symbol': 'USDC',
                'token0_address': '******************************************',
                'token1_address': '******************************************',
                'liquidity_usd': 5000000,
                'volume_24h_usd': 1200000,
                'tx_count': 2000
            },
            {
                'token0_symbol': 'SCAM',
                'token1_symbol': 'TOKEN',
                'token0_address': '0x123',
                'token1_address': '0x456',
                'liquidity_usd': 50000,  # Below minimum
                'volume_24h_usd': 10000,
                'tx_count': 10
            }
        ]
        
        print("Filtering mock assets...")
        filtered = await selector._filter_assets(mock_assets)
        print(f"Assets before filtering: {len(mock_assets)}")
        print(f"Assets after filtering: {len(filtered)}")
        
        for asset in filtered:
            print(f"  - {asset['token0_symbol']}/{asset['token1_symbol']}: ${asset['liquidity_usd']:,.0f} liquidity")
        
        # Demo sentiment scoring
        print("\nTesting sentiment scoring...")
        sample_sentiment = {
            'fear_greed_index': 75,
            'avg_funding_rate': 0.01,
            'long_short_ratio': 1.2,
            'liquidation_24h': 500000
        }
        
        sentiment_score = selector._calculate_sentiment_score(sample_sentiment)
        print(f"Sentiment score: {sentiment_score:.2f} (0=bearish, 1=bullish)")
        
        return True
        
    except Exception as e:
        print(f"❌ Asset selector demo failed: {e}")
        return False

async def demo_price_aggregator():
    """Demonstrate price aggregation logic"""
    print("\n💰 Price Aggregator Demo")
    print("-" * 30)
    
    try:
        from src.data_collectors.price_aggregator import PriceAggregator
        
        aggregator = PriceAggregator()
        
        # Demo price aggregation with mock sources
        mock_sources = [
            {
                'source': 'quickswap',
                'price': 0.85,
                'liquidity_usd': 1000000,
                'timestamp': datetime.now().isoformat()
            },
            {
                'source': 'uniswap_v3',
                'price': 0.87,
                'liquidity_usd': 2000000,
                'timestamp': datetime.now().isoformat()
            }
        ]
        
        print("Aggregating prices from mock sources...")
        aggregated = aggregator._aggregate_prices(mock_sources)
        
        print(f"Aggregated price: ${aggregated['price']:.4f}")
        print(f"Confidence: {aggregated['confidence']:.2f}")
        print(f"Method: {aggregated['aggregation_method']}")
        print(f"Sources used: {len(aggregated['sources'])}")
        
        # Demo with outlier
        print("\nTesting outlier detection...")
        mock_sources_with_outlier = mock_sources + [
            {
                'source': 'outlier_exchange',
                'price': 1.50,  # Significant outlier
                'liquidity_usd': 100000,
                'timestamp': datetime.now().isoformat()
            }
        ]
        
        aggregated_with_outlier = aggregator._aggregate_prices(mock_sources_with_outlier)
        print(f"Price with outlier: ${aggregated_with_outlier['price']:.4f}")
        print(f"Outliers removed: {aggregated_with_outlier['outliers_removed']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Price aggregator demo failed: {e}")
        return False

async def main():
    """Run Phase 2 demonstration"""
    print("🚀 Phase 2 Data Infrastructure Demo")
    print("=" * 50)
    print("This demo shows Phase 2 capabilities without external API calls")
    print()
    
    demos = [
        ("Cache System", demo_cache_system),
        ("Data Validator", demo_data_validator),
        ("Asset Selector", demo_asset_selector),
        ("Price Aggregator", demo_price_aggregator)
    ]
    
    results = []
    
    for demo_name, demo_func in demos:
        try:
            result = await demo_func()
            results.append((demo_name, result))
        except Exception as e:
            print(f"❌ {demo_name} demo failed with exception: {e}")
            results.append((demo_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Demo Results Summary:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for demo_name, result in results:
        status = "✅ SUCCESS" if result else "❌ FAILED"
        print(f"  {demo_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} demos successful")
    
    if passed == total:
        print("🎉 Phase 2 data infrastructure is working correctly!")
        print("\n📋 Next Steps:")
        print("  1. Configure API keys in .env file for live data")
        print("  2. Run 'python main.py' to start the bot with real-time data")
        print("  3. Proceed to Phase 3: ML Models & AI Integration")
    else:
        print("⚠️  Some demos failed. Check the error messages above.")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
