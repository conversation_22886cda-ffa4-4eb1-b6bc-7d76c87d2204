#!/usr/bin/env python3
"""
Phase 4 Testing Script - Paper Trading Engine
Comprehensive tests for realistic paper trading, risk management, and performance analytics
"""

import asyncio
import sys
import os
import json
from datetime import datetime, timedelta

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import Config
from src.paper_trading.portfolio_manager import PortfolioManager
from src.paper_trading.trading_engine import TradingEngine
from src.paper_trading.risk_manager import RiskManager
from src.paper_trading.performance_analytics import PerformanceAnalytics


async def test_portfolio_manager():
    """Test portfolio manager functionality"""
    print("\n🏦 Testing Portfolio Manager")
    print("-" * 40)
    
    try:
        config = Config()
        portfolio = PortfolioManager(config)
        
        # Test initial state
        summary = portfolio.get_portfolio_summary()
        print(f"✅ Initial portfolio value: ${summary['portfolio_value']['total_value']:.2f}")
        print(f"✅ Initial balance: ${summary['portfolio_value']['current_balance']:.2f}")
        
        # Create test signal
        test_signal = {
            'asset': 'MATIC/USDC',
            'signal_direction': 'BUY',
            'entry_price': 0.85,
            'confidence': 0.75,
            'signal_strength': 0.68,
            'target_price': 0.88,
            'stop_loss': 0.82,
            'max_hold_time': 10,
            'timestamp': datetime.now().isoformat()
        }
        
        # Test market conditions
        market_conditions = {
            'condition': 'normal',
            'total_liquidity': 150000,
            'volume_24h': 75000,
            'price_volatility': 0.015,
            'confidence': 0.8
        }
        
        # Execute test trade
        print(f"\n📊 Executing test trade: {test_signal['asset']} {test_signal['signal_direction']}")
        trade_result = await portfolio.execute_trade(test_signal, market_conditions)
        
        if trade_result['status'] == 'filled':
            print(f"✅ Trade executed successfully!")
            print(f"   Fill Price: ${trade_result['fill_price']:.6f}")
            print(f"   Quantity: {trade_result['quantity']:.4f}")
            print(f"   Gas Cost: ${trade_result['gas_cost']:.2f}")
            print(f"   Slippage Cost: ${trade_result['slippage_cost']:.4f}")
            print(f"   Risk Level: {trade_result['risk_assessment'].risk_level.value}")
        else:
            print(f"❌ Trade failed: {trade_result['reason']}")
            return False
        
        # Test position update
        print(f"\n🔄 Testing position updates...")
        update_result = await portfolio.update_positions()
        print(f"✅ Updated {update_result['updated_positions']} positions")
        
        # Test portfolio summary
        updated_summary = portfolio.get_portfolio_summary()
        print(f"✅ Portfolio after trade: ${updated_summary['portfolio_value']['total_value']:.2f}")
        print(f"✅ Open positions: {updated_summary['positions']['open_positions']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Portfolio manager test failed: {e}")
        return False


async def test_trading_engine():
    """Test realistic trading engine"""
    print("\n⚙️ Testing Trading Engine")
    print("-" * 40)
    
    try:
        config = Config()
        engine = TradingEngine(config)
        
        # Test trade execution simulation
        print("📈 Simulating trade execution...")
        
        execution_result = await engine.simulate_trade_execution(
            asset='MATIC/USDC',
            direction='BUY',
            size_usd=500.0,
            target_price=0.85
        )
        
        if execution_result['success']:
            print(f"✅ Execution simulation successful!")
            print(f"   Execution Price: ${execution_result['execution_price']:.6f}")
            print(f"   Executed Quantity: {execution_result['executed_quantity']:.4f}")
            print(f"   Gas Cost: ${execution_result['gas_cost']:.2f}")
            print(f"   Slippage: {execution_result['market_impact']['slippage_pct']*100:.3f}%")
            print(f"   Execution Delay: {execution_result['execution_delay_ms']}ms")
            
            if execution_result['partial_fill']['is_partial']:
                print(f"   Partial Fill: {execution_result['partial_fill']['fill_ratio']*100:.1f}%")
        else:
            print(f"❌ Execution simulation failed: {execution_result['error']}")
            return False
        
        # Test different market conditions
        print(f"\n🌊 Testing volatile market conditions...")
        
        volatile_result = await engine.simulate_trade_execution(
            asset='MATIC/USDC',
            direction='SELL',
            size_usd=1000.0,
            target_price=0.85
        )
        
        if volatile_result['success']:
            print(f"✅ Volatile market simulation successful!")
            print(f"   Higher slippage: {volatile_result['market_impact']['slippage_pct']*100:.3f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Trading engine test failed: {e}")
        return False


def test_risk_manager():
    """Test risk management system"""
    print("\n🛡️ Testing Risk Manager")
    print("-" * 40)
    
    try:
        config = Config()
        risk_manager = RiskManager(config)
        
        # Test low-risk signal
        low_risk_signal = {
            'asset': 'MATIC/USDC',
            'signal_direction': 'BUY',
            'entry_price': 0.85,
            'confidence': 0.85,
            'signal_strength': 0.80,
            'timestamp': datetime.now().isoformat()
        }
        
        portfolio_state = {
            'portfolio_value': {'total_value': 10000, 'current_balance': 9500},
            'positions': {'open_positions': 1, 'position_details': []},
            'risk_metrics': {'current_exposure_pct': 5}
        }
        
        market_conditions = {
            'condition': 'normal',
            'total_liquidity': 200000,
            'price_volatility': 0.01
        }
        
        print("📊 Assessing low-risk trade...")
        risk_assessment = risk_manager.assess_trade_risk(
            low_risk_signal, portfolio_state, market_conditions
        )
        
        print(f"✅ Risk Level: {risk_assessment.risk_level.value}")
        print(f"✅ Risk Score: {risk_assessment.risk_score:.1f}")
        print(f"✅ Position Multiplier: {risk_assessment.position_size_multiplier:.2f}")
        print(f"✅ Recommended Stop Loss: ${risk_assessment.recommended_stop_loss:.4f}")
        
        # Test high-risk signal
        high_risk_signal = {
            'asset': 'MATIC/USDC',
            'signal_direction': 'BUY',
            'entry_price': 0.85,
            'confidence': 0.45,
            'signal_strength': 0.40,
            'timestamp': (datetime.now() - timedelta(minutes=10)).isoformat()
        }
        
        high_risk_portfolio = {
            'portfolio_value': {'total_value': 10000, 'current_balance': 2000},
            'positions': {'open_positions': 8, 'position_details': []},
            'risk_metrics': {'current_exposure_pct': 80}
        }
        
        volatile_market = {
            'condition': 'volatile',
            'total_liquidity': 30000,
            'price_volatility': 0.05
        }
        
        print(f"\n⚠️ Assessing high-risk trade...")
        high_risk_assessment = risk_manager.assess_trade_risk(
            high_risk_signal, high_risk_portfolio, volatile_market
        )
        
        print(f"✅ Risk Level: {high_risk_assessment.risk_level.value}")
        print(f"✅ Risk Score: {high_risk_assessment.risk_score:.1f}")
        print(f"✅ Position Multiplier: {high_risk_assessment.position_size_multiplier:.2f}")
        print(f"✅ Warnings: {len(high_risk_assessment.warnings)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Risk manager test failed: {e}")
        return False


def test_performance_analytics():
    """Test performance analytics"""
    print("\n📈 Testing Performance Analytics")
    print("-" * 40)
    
    try:
        config = Config()
        analytics = PerformanceAnalytics(config)
        
        # Create sample trade history
        sample_trades = [
            {
                'trade_id': 'trade_001',
                'asset': 'MATIC/USDC',
                'direction': 'long',
                'entry_price': 0.80,
                'exit_price': 0.85,
                'quantity': 1000,
                'entry_time': (datetime.now() - timedelta(hours=2)).isoformat(),
                'exit_time': (datetime.now() - timedelta(hours=1, minutes=45)).isoformat(),
                'hold_time_minutes': 15,
                'realized_pnl': 45.50,
                'gas_cost': 0.75,
                'slippage_cost': 1.25,
                'exit_reason': 'target_reached',
                'success': True
            },
            {
                'trade_id': 'trade_002',
                'asset': 'WETH/USDC',
                'direction': 'long',
                'entry_price': 2500,
                'exit_price': 2480,
                'quantity': 0.2,
                'entry_time': (datetime.now() - timedelta(hours=1)).isoformat(),
                'exit_time': (datetime.now() - timedelta(minutes=30)).isoformat(),
                'hold_time_minutes': 30,
                'realized_pnl': -4.75,
                'gas_cost': 0.80,
                'slippage_cost': 0.95,
                'exit_reason': 'stop_loss',
                'success': True
            }
        ]
        
        # Calculate performance metrics
        print("📊 Calculating performance metrics...")
        metrics = analytics.calculate_performance_metrics(
            sample_trades, 
            initial_balance=10000, 
            current_balance=10040.75
        )
        
        print(f"✅ Total Return: ${metrics.total_return:.2f} ({metrics.total_return_pct:.2f}%)")
        print(f"✅ Win Rate: {metrics.win_rate_pct:.1f}%")
        print(f"✅ Sharpe Ratio: {metrics.sharpe_ratio:.3f}")
        print(f"✅ Average Trade P&L: ${metrics.avg_trade_pnl:.2f}")
        print(f"✅ Average Hold Time: {metrics.avg_hold_time_minutes:.1f} minutes")
        
        # Analyze trading patterns
        print(f"\n🔍 Analyzing trading patterns...")
        patterns = analytics.analyze_trading_patterns(sample_trades)
        
        if 'asset_performance' in patterns:
            print(f"✅ Asset analysis completed for {len(patterns['asset_performance'])} assets")
        
        if 'exit_reason_analysis' in patterns:
            print(f"✅ Exit reason analysis: {len(patterns['exit_reason_analysis'])} categories")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance analytics test failed: {e}")
        return False


async def run_integration_test():
    """Run full integration test"""
    print("\n🔗 Running Integration Test")
    print("-" * 40)
    
    try:
        config = Config()
        portfolio = PortfolioManager(config)
        analytics = PerformanceAnalytics(config)
        
        # Execute multiple trades
        test_signals = [
            {
                'asset': 'MATIC/USDC',
                'signal_direction': 'BUY',
                'entry_price': 0.85,
                'confidence': 0.75,
                'signal_strength': 0.68,
                'timestamp': datetime.now().isoformat()
            },
            {
                'asset': 'WETH/USDC',
                'signal_direction': 'SELL',
                'entry_price': 2500,
                'confidence': 0.65,
                'signal_strength': 0.72,
                'timestamp': datetime.now().isoformat()
            }
        ]
        
        executed_trades = 0
        for signal in test_signals:
            trade_result = await portfolio.execute_trade(signal)
            if trade_result['status'] == 'filled':
                executed_trades += 1
        
        print(f"✅ Executed {executed_trades}/{len(test_signals)} trades")
        
        # Get final portfolio state
        final_summary = portfolio.get_portfolio_summary()
        print(f"✅ Final portfolio value: ${final_summary['portfolio_value']['total_value']:.2f}")
        print(f"✅ Total trades: {final_summary['performance']['total_trades']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False


async def main():
    """Run all Phase 4 tests"""
    print("🚀 Phase 4 Paper Trading Engine Tests")
    print("=" * 50)
    
    tests = [
        ("Portfolio Manager", test_portfolio_manager),
        ("Trading Engine", test_trading_engine),
        ("Risk Manager", test_risk_manager),
        ("Performance Analytics", test_performance_analytics),
        ("Integration Test", run_integration_test)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                print(f"✅ {test_name} test PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"❌ {test_name} test ERROR: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All Phase 4 tests PASSED! Paper trading system is ready.")
    else:
        print("⚠️ Some tests failed. Please review the implementation.")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(main())
