#!/usr/bin/env python3
"""
Quick status check for the Signal Bot
"""

import subprocess
import os
import json
from datetime import datetime

def check_status():
    """Check bot status and show summary"""
    print("🤖 SIGNAL BOT STATUS CHECK")
    print("=" * 40)
    
    # Check if process is running
    try:
        result = subprocess.run(['pgrep', '-f', 'main.py'], capture_output=True, text=True)
        if result.stdout.strip():
            print("✅ Bot is RUNNING")
            print(f"   Process ID: {result.stdout.strip()}")
        else:
            print("❌ Bot is NOT running")
            return
    except:
        print("❓ Cannot determine bot status")
        return
    
    # Check log files
    if os.path.exists('logs/signal_bot.log'):
        size = os.path.getsize('logs/signal_bot.log')
        print(f"📝 Log file: {size/1024/1024:.1f} MB")
    
    # Get latest performance data
    try:
        if os.path.exists('logs/performance.json'):
            with open('logs/performance.json', 'r') as f:
                lines = f.readlines()
                if lines:
                    perf = json.loads(lines[-1])
                    print(f"📊 Data Quality: {perf.get('data_quality_score', 'N/A')}")
                    print(f"🎯 Assets: {perf.get('assets_processed', 'N/A')}")
                    print(f"⏱️  Last Update: {perf.get('timestamp', 'N/A')[:19]}")
    except:
        pass
    
    # Check recent activity
    try:
        if os.path.exists('logs/signal_bot.log'):
            with open('logs/signal_bot.log', 'r') as f:
                lines = f.readlines()
                if lines:
                    last_line = lines[-1]
                    print(f"🔄 Last Activity: {last_line.split(' - ')[0][-8:]}")
    except:
        pass
    
    print("\n💡 To monitor live: python3 monitor.py")
    print("💡 To stop bot: pkill -f main.py")

if __name__ == "__main__":
    check_status()
