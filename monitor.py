#!/usr/bin/env python3
"""
Real-time monitoring dashboard for the Signal Bot
Shows key metrics, system health, and recent activity
"""

import time
import json
import os
from datetime import datetime
import subprocess

def clear_screen():
    os.system('clear' if os.name == 'posix' else 'cls')

def get_system_stats():
    """Get basic system statistics"""
    try:
        # Check if main process is running
        result = subprocess.run(['pgrep', '-f', 'main.py'], capture_output=True, text=True)
        process_running = bool(result.stdout.strip())
        
        # Get log file sizes
        log_size = 0
        if os.path.exists('logs/signal_bot.log'):
            log_size = os.path.getsize('logs/signal_bot.log')
        
        return {
            'process_running': process_running,
            'log_size_mb': round(log_size / 1024 / 1024, 2),
            'uptime': 'Running' if process_running else 'Stopped'
        }
    except:
        return {'process_running': False, 'log_size_mb': 0, 'uptime': 'Unknown'}

def get_recent_logs(lines=10):
    """Get recent log entries"""
    try:
        if os.path.exists('logs/signal_bot.log'):
            with open('logs/signal_bot.log', 'r') as f:
                all_lines = f.readlines()
                return all_lines[-lines:] if all_lines else []
        return []
    except:
        return []

def get_performance_data():
    """Get latest performance metrics"""
    try:
        if os.path.exists('logs/performance.json'):
            with open('logs/performance.json', 'r') as f:
                lines = f.readlines()
                if lines:
                    return json.loads(lines[-1])
        return {}
    except:
        return {}

def get_signals_data():
    """Get recent signals"""
    try:
        if os.path.exists('logs/signals.json'):
            with open('logs/signals.json', 'r') as f:
                lines = f.readlines()
                signals = []
                for line in lines[-5:]:  # Last 5 signals
                    try:
                        signals.append(json.loads(line))
                    except:
                        continue
                return signals
        return []
    except:
        return []

def display_dashboard():
    """Display the monitoring dashboard"""
    clear_screen()
    
    print("🚀 SIGNAL BOT LIVE MONITORING DASHBOARD")
    print("=" * 60)
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # System Status
    stats = get_system_stats()
    status_icon = "🟢" if stats['process_running'] else "🔴"
    print(f"{status_icon} SYSTEM STATUS")
    print(f"   Process: {stats['uptime']}")
    print(f"   Log Size: {stats['log_size_mb']} MB")
    print()
    
    # Performance Metrics
    perf = get_performance_data()
    if perf:
        print("📊 PERFORMANCE METRICS")
        print(f"   Market Data Quality: {perf.get('data_quality_score', 'N/A')}")
        print(f"   Assets Processed: {perf.get('assets_processed', 'N/A')}")
        print(f"   Cycle Time: {perf.get('cycle_time_ms', 'N/A')} ms")
        print(f"   Memory Usage: {perf.get('memory_usage_mb', 'N/A')} MB")
    else:
        print("📊 PERFORMANCE METRICS: No data available")
    print()
    
    # Recent Signals
    signals = get_signals_data()
    print("🎯 RECENT SIGNALS")
    if signals:
        for signal in signals[-3:]:  # Show last 3
            timestamp = signal.get('timestamp', 'Unknown')
            symbol = signal.get('symbol', 'Unknown')
            direction = signal.get('signal_direction', 'Unknown')
            confidence = signal.get('confidence', 0)
            print(f"   {timestamp[:19]} | {symbol} | {direction} | {confidence:.2f}")
    else:
        print("   No signals generated yet (system learning)")
    print()
    
    # Recent Log Activity
    logs = get_recent_logs(5)
    print("📝 RECENT ACTIVITY")
    for log in logs:
        if log.strip():
            # Extract timestamp and message
            parts = log.strip().split(' - ', 2)
            if len(parts) >= 3:
                timestamp = parts[0]
                level = parts[1]
                message = parts[2][:50] + "..." if len(parts[2]) > 50 else parts[2]
                print(f"   {timestamp[-8:]} | {level:5} | {message}")
    print()
    
    print("🔄 Auto-refreshing every 5 seconds... (Ctrl+C to exit)")
    print("=" * 60)

def main():
    """Main monitoring loop"""
    try:
        while True:
            display_dashboard()
            time.sleep(5)  # Refresh every 5 seconds
    except KeyboardInterrupt:
        print("\n\n👋 Monitoring stopped. Bot continues running in background.")
        print("To stop the bot: pkill -f main.py")

if __name__ == "__main__":
    main()
