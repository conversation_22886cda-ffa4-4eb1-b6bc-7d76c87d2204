# Makefile for Polygon MATIC Signal Bot

.PHONY: help install test lint format clean run setup

# Default target
help:
	@echo "Available commands:"
	@echo "  setup     - Set up the development environment"
	@echo "  install   - Install dependencies"
	@echo "  test      - Run tests"
	@echo "  lint      - Run linting"
	@echo "  format    - Format code"
	@echo "  clean     - Clean up generated files"
	@echo "  run       - Run the signal bot"
	@echo "  test-setup - Test Phase 1 setup"

# Set up development environment
setup:
	python3 -m venv venv
	./venv/bin/pip install --upgrade pip
	./venv/bin/pip install -r requirements.txt
	cp .env.example .env
	@echo "Setup complete! Don't forget to configure your .env file"

# Install dependencies
install:
	./venv/bin/pip install -r requirements.txt

# Run tests
test:
	./venv/bin/python -m pytest tests/ -v

# Test Phase 1 setup
test-setup:
	./venv/bin/python scripts/test_setup.py

# Run linting
lint:
	./venv/bin/flake8 src/ tests/ --max-line-length=100
	./venv/bin/mypy src/ --ignore-missing-imports

# Format code
format:
	./venv/bin/black src/ tests/ scripts/ --line-length=100

# Clean up
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/

# Run the signal bot
run:
	./venv/bin/python main.py

# Development mode (with auto-reload)
dev:
	./venv/bin/python -m watchdog main.py
