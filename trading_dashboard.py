#!/usr/bin/env python3
"""
Comprehensive Trading Dashboard
Shows trades, P&L, positions, gas fees, and performance metrics
"""

import time
import json
import os
import csv
from datetime import datetime
import subprocess

def clear_screen():
    os.system('clear' if os.name == 'posix' else 'cls')

def load_portfolio_data():
    """Load current portfolio state"""
    try:
        if os.path.exists('data/portfolio_state.json'):
            with open('data/portfolio_state.json', 'r') as f:
                return json.load(f)
        return {
            'total_value': 10000.0,  # Starting capital
            'available_balance': 10000.0,
            'positions': {},
            'total_pnl': 0.0,
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0
        }
    except:
        return {'total_value': 10000.0, 'available_balance': 10000.0, 'positions': {}, 'total_pnl': 0.0}

def load_trades_data():
    """Load recent trades from CSV"""
    trades = []
    try:
        if os.path.exists('logs/trades.csv'):
            with open('logs/trades.csv', 'r') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    if row.get('symbol'):  # Skip empty rows
                        trades.append(row)
        return trades[-20:]  # Last 20 trades
    except:
        return []

def load_signals_data():
    """Load recent signals"""
    try:
        if os.path.exists('logs/signals.json'):
            with open('logs/signals.json', 'r') as f:
                content = f.read().strip()
                if content:
                    # Handle both single objects and arrays
                    if content.startswith('['):
                        return json.loads(content)
                    else:
                        # Multiple JSON objects, one per line
                        signals = []
                        for line in content.split('\n'):
                            if line.strip():
                                try:
                                    signals.append(json.loads(line))
                                except:
                                    continue
                        return signals
        return []
    except:
        return []

def get_system_status():
    """Check if trading system is running"""
    try:
        result = subprocess.run(['pgrep', '-f', 'main.py'], capture_output=True, text=True)
        return bool(result.stdout.strip())
    except:
        return False

def calculate_performance_metrics(trades, portfolio):
    """Calculate key performance metrics"""
    if not trades:
        return {
            'total_trades': 0,
            'win_rate': 0,
            'total_pnl': 0,
            'avg_trade_pnl': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'max_drawdown': 0,
            'sharpe_ratio': 0
        }
    
    total_pnl = 0
    winning_trades = 0
    losing_trades = 0
    
    for trade in trades:
        try:
            pnl = float(trade.get('pnl', 0))
            total_pnl += pnl
            if pnl > 0:
                winning_trades += 1
            elif pnl < 0:
                losing_trades += 1
        except:
            continue
    
    total_trades = len(trades)
    win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
    avg_trade_pnl = total_pnl / total_trades if total_trades > 0 else 0
    
    return {
        'total_trades': total_trades,
        'win_rate': win_rate,
        'total_pnl': total_pnl,
        'avg_trade_pnl': avg_trade_pnl,
        'winning_trades': winning_trades,
        'losing_trades': losing_trades
    }

def display_trading_dashboard():
    """Display comprehensive trading dashboard"""
    clear_screen()
    
    print("💰 SIGNAL BOT TRADING DASHBOARD")
    print("=" * 80)
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # System Status
    system_running = get_system_status()
    status_icon = "🟢" if system_running else "🔴"
    print(f"{status_icon} System: {'LIVE' if system_running else 'OFFLINE'}")
    print()
    
    # Portfolio Overview
    portfolio = load_portfolio_data()
    print("📊 PORTFOLIO OVERVIEW")
    print(f"   💵 Total Value: ${portfolio.get('total_value', 10000):,.2f}")
    print(f"   💰 Available Balance: ${portfolio.get('available_balance', 10000):,.2f}")
    print(f"   📈 Total P&L: ${portfolio.get('total_pnl', 0):+,.2f}")
    print(f"   📊 ROI: {(portfolio.get('total_pnl', 0) / 10000 * 100):+.2f}%")
    print()
    
    # Load trades and signals
    trades = load_trades_data()
    signals = load_signals_data()
    
    # Performance Metrics
    metrics = calculate_performance_metrics(trades, portfolio)
    print("📈 PERFORMANCE METRICS")
    print(f"   🎯 Total Trades: {metrics['total_trades']}")
    print(f"   ✅ Win Rate: {metrics['win_rate']:.1f}%")
    print(f"   💹 Avg Trade P&L: ${metrics['avg_trade_pnl']:+.2f}")
    print(f"   🏆 Winning Trades: {metrics['winning_trades']}")
    print(f"   📉 Losing Trades: {metrics['losing_trades']}")
    print()
    
    # Active Positions
    positions = portfolio.get('positions', {})
    print("🎯 ACTIVE POSITIONS")
    if positions:
        for symbol, position in positions.items():
            pnl = position.get('unrealized_pnl', 0)
            pnl_color = "🟢" if pnl >= 0 else "🔴"
            print(f"   {pnl_color} {symbol}: {position.get('quantity', 0):.4f} @ ${position.get('entry_price', 0):.4f} | P&L: ${pnl:+.2f}")
    else:
        print("   No active positions")
    print()
    
    # Recent Signals
    print("🎯 RECENT SIGNALS")
    if signals:
        for signal in signals[-5:]:  # Last 5 signals
            timestamp = signal.get('timestamp', 'Unknown')[:19]
            asset = signal.get('asset', signal.get('symbol', 'Unknown'))
            action = signal.get('action', signal.get('signal_direction', 'Unknown'))
            confidence = signal.get('confidence', 0)
            price = signal.get('entry_price', signal.get('price', 0))
            print(f"   {timestamp} | {asset:12} | {action:4} | ${price:.4f} | Conf: {confidence:.2f}")
    else:
        print("   No signals generated yet (system learning)")
    print()
    
    # Recent Trades
    print("💼 RECENT TRADES")
    if trades:
        print("   Time     | Symbol      | Side | Entry   | Exit    | Qty    | P&L     | Gas   | Status")
        print("   " + "-" * 75)
        for trade in trades[-10:]:  # Last 10 trades
            timestamp = trade.get('timestamp', 'Unknown')[:8]
            symbol = trade.get('symbol', 'Unknown')[:10]
            side = trade.get('side', 'Unknown')[:4]
            entry = float(trade.get('entry_price', 0))
            exit_price = trade.get('exit_price', '')
            exit_price = float(exit_price) if exit_price else 0
            quantity = float(trade.get('quantity', 0))
            pnl = float(trade.get('pnl', 0))
            gas_fee = float(trade.get('gas_fee', 0))
            status = trade.get('status', 'Unknown')[:12]
            
            pnl_str = f"${pnl:+6.2f}" if pnl != 0 else "  -    "
            exit_str = f"${exit_price:.4f}" if exit_price > 0 else "  -    "
            gas_str = f"${gas_fee:.2f}" if gas_fee > 0 else " -   "
            
            print(f"   {timestamp} | {symbol:11} | {side:4} | ${entry:6.4f} | {exit_str} | {quantity:6.3f} | {pnl_str} | {gas_str} | {status}")
    else:
        print("   No trades executed yet")
    print()
    
    # Gas Fee Summary
    total_gas = sum(float(trade.get('gas_fee', 0)) for trade in trades)
    print("⛽ GAS FEES")
    print(f"   Total Gas Spent: ${total_gas:.2f}")
    print(f"   Avg Gas per Trade: ${total_gas / len(trades) if trades else 0:.2f}")
    print()
    
    print("🔄 Auto-refreshing every 3 seconds... (Ctrl+C to exit)")
    print("=" * 80)

def main():
    """Main dashboard loop"""
    try:
        while True:
            display_trading_dashboard()
            time.sleep(3)  # Refresh every 3 seconds
    except KeyboardInterrupt:
        print("\n\n👋 Dashboard closed. Trading bot continues running.")

if __name__ == "__main__":
    main()
