#!/usr/bin/env python3
"""
Polygon MATIC Hyper-Short-Term Signal Bot
Main entry point for the AI-powered trading signal bot
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, Any, List

# Import Phase 2 data infrastructure
from src.data_collectors.polygon_rpc import polygon_rpc
# Deprecated collectors replaced with simple_asset_provider
from src.data_collectors.alchemy_market_client import alchemy_market_client
from src.data_collectors.simple_asset_provider import simple_asset_provider
from src.data_collectors.cache_manager import cache_manager
from src.data_collectors.price_aggregator import price_aggregator
from src.data_collectors.asset_selector import asset_selector
from src.data_collectors.data_validator import data_validator

# Import Phase 3 ML components
from src.ml_models.inference_engine import inference_engine
from src.ml_models.model_trainer import model_trainer

from src.signal_engine.signal_generator import SignalGenerator
from src.paper_trading.portfolio_manager import PortfolioManager
from src.paper_trading.performance_analytics import PerformanceAnalytics
from src.logging_system.trade_logger import TradeLogger
from config.settings import Config


class SignalBot:
    """Main Signal Bot orchestrator with Phase 2 data infrastructure and Phase 3 AI/ML"""

    def __init__(self):
        self.config = Config()
        self.signal_generator = SignalGenerator(self.config)
        self.portfolio_manager = PortfolioManager(self.config)
        self.performance_analytics = PerformanceAnalytics(self.config)
        self.trade_logger = TradeLogger(self.config)
        self.running = False

        # Phase 2 components
        self.data_systems_healthy = False

        # Phase 3 components
        self.ml_systems_healthy = False

        # Phase 4 components
        self.paper_trading_enabled = True

        # Set up logging
        logging.basicConfig(
            level=getattr(logging, self.config.LOG_LEVEL),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/signal_bot.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    async def scan_assets(self) -> Dict[str, Any]:
        """Scan and filter assets for trading opportunities using AI-powered selection"""
        try:
            return await self.signal_generator.scan_assets()
        except Exception as e:
            self.logger.error(f"Error scanning assets: {e}")
            return {}

    async def generate_signals(self, scan_result: Dict[str, Any]) -> list:
        """Generate AI-powered trading signals using LSTM and DeepSeek analysis"""
        try:
            return await self.signal_generator.generate_signals(scan_result)
        except Exception as e:
            self.logger.error(f"Error generating signals: {e}")
            return []
    
    async def execute_signals(self, signals: list, market_data: Dict[str, Any] = None):
        """Execute paper trades based on signals with Phase 4 enhancements"""
        if not self.paper_trading_enabled:
            self.logger.info("Paper trading disabled - skipping signal execution")
            return

        executed_trades = []

        for signal in signals:
            try:
                # Extract market conditions for this asset if available
                asset_market_conditions = None
                if market_data and 'top_assets' in market_data:
                    for asset_data in market_data['top_assets']:
                        if asset_data.get('symbol') == signal.get('asset'):
                            asset_market_conditions = {
                                'condition': 'normal',  # Could be enhanced with actual condition detection
                                'total_liquidity': asset_data.get('liquidity_usd', 100000),
                                'volume_24h': asset_data.get('volume_24h', 50000),
                                'price_volatility': 0.01,  # Default volatility
                                'confidence': asset_data.get('sentiment_score', 0.5)
                            }
                            break

                # Execute trade with market conditions
                trade_result = await self.portfolio_manager.execute_trade(signal, asset_market_conditions)

                # Log trade result
                await self.trade_logger.log_trade(signal, trade_result)

                # Track executed trades
                if trade_result.get('status') == 'filled':
                    executed_trades.append(trade_result)

                    # Log detailed execution info
                    risk_level = trade_result.get('risk_assessment', {}).get('risk_level', 'unknown')
                    self.logger.info(f"Trade filled: {signal['asset']} ${trade_result['executed_size']:.2f} "
                                   f"risk={risk_level} slippage={trade_result['slippage_cost']:.2f}")

            except Exception as e:
                self.logger.error(f"Error executing signal {signal.get('asset', 'unknown')}: {e}")

        # Update all positions after execution
        if executed_trades:
            try:
                update_result = await self.portfolio_manager.update_positions()
                if update_result['closed_positions'] > 0:
                    self.logger.info(f"Closed {update_result['closed_positions']} positions during update")
            except Exception as e:
                self.logger.error(f"Error updating positions: {e}")

        return executed_trades
    
    async def run_cycle(self):
        """Run one complete bot cycle with Phase 2 data collection and Phase 3 AI analysis"""
        self.logger.info("Starting AI-powered bot cycle")

        if not self.data_systems_healthy:
            self.logger.warning("Data systems unhealthy - running in degraded mode")
            await self._run_placeholder_cycle()
            return

        if not self.ml_systems_healthy:
            self.logger.warning("ML systems unhealthy - running without AI predictions")

        try:
            # Phase 2: Collect real market data
            market_data = await self._collect_market_data()

            # Validate data quality
            data_quality = await self._validate_market_data(market_data)
            self.logger.info(f"Market data quality score: {data_quality.get('overall_quality', 0):.2f}")

            if data_quality.get('overall_quality', 0) < 0.3:
                self.logger.warning("Poor data quality - skipping cycle")
                return

            # Phase 3: AI-powered asset scanning
            scan_result = await self.scan_assets()

            if not scan_result.get('assets'):
                self.logger.info("No assets found for analysis")
                return

            # Phase 3: Generate AI-powered signals
            signals = await self.generate_signals(scan_result)
            if not signals:
                self.logger.info("No AI signals generated")
                return

            # Log signal summary
            self._log_signal_summary(signals)

            # Execute signals (Phase 4 - paper trading with risk management)
            executed_trades = await self.execute_signals(signals, market_data)

            # Log portfolio performance summary every 10 cycles
            if hasattr(self, '_cycle_count'):
                self._cycle_count += 1
            else:
                self._cycle_count = 1

            if self._cycle_count % 10 == 0:
                await self._log_portfolio_performance()

            self.logger.info(f"AI cycle completed - processed {len(signals)} signals, executed {len(executed_trades) if executed_trades else 0} trades from {scan_result.get('total_filtered', 0)} assets")

        except Exception as e:
            self.logger.error(f"Error in AI-powered cycle: {e}")
            # Fall back to placeholder behavior
            await self._run_placeholder_cycle()

    async def _run_placeholder_cycle(self):
        """Run placeholder cycle when data systems are unavailable"""
        # Original Phase 1 behavior
        assets = await self.scan_assets()
        if not assets:
            self.logger.info("No assets found for analysis (placeholder)")
            return

        signals = await self.generate_signals(assets)
        if not signals:
            self.logger.info("No signals generated (placeholder)")
            return

        await self.execute_signals(signals)
        self.logger.info(f"Placeholder cycle completed - processed {len(signals)} signals")

    def _log_signal_summary(self, signals: List[Dict[str, Any]]):
        """Log summary of generated signals"""

        if not signals:
            return

        buy_signals = [s for s in signals if s['signal_direction'] == 'BUY']
        sell_signals = [s for s in signals if s['signal_direction'] == 'SELL']

        avg_confidence = sum(s['confidence'] for s in signals) / len(signals)
        avg_strength = sum(s['signal_strength'] for s in signals) / len(signals)

        high_confidence = [s for s in signals if s['confidence'] > 0.8]

        self.logger.info(f"Signal Summary: {len(buy_signals)} BUY, {len(sell_signals)} SELL")
        self.logger.info(f"Average confidence: {avg_confidence:.3f}, strength: {avg_strength:.3f}")
        self.logger.info(f"High confidence signals (>0.8): {len(high_confidence)}")

        # Log top signals
        top_signals = sorted(signals, key=lambda x: x['confidence'] * x['signal_strength'], reverse=True)[:3]
        for i, signal in enumerate(top_signals, 1):
            self.logger.info(f"Top signal #{i}: {signal['asset']} {signal['signal_direction']} "
                           f"(conf: {signal['confidence']:.3f}, strength: {signal['signal_strength']:.3f})")

    async def _collect_market_data(self) -> Dict[str, Any]:
        """Collect comprehensive market data using Phase 2 systems"""
        try:
            # Get top trading assets
            top_assets = await asset_selector.get_top_assets()

            # Get current gas prices
            gas_prices = await polygon_rpc.get_gas_price()

            # Get market sentiment for MATIC using Alchemy
            sentiment_data = {}
            if alchemy_market_client:
                sentiment_data = await alchemy_market_client.get_market_sentiment_summary("MATIC")

            # Get sample price data
            price_data = {}
            if top_assets and len(top_assets) > 0:
                sample_asset = top_assets[0]
                if sample_asset.get('trading_token'):
                    token_address = sample_asset['trading_token']['address']
                    price_info = await price_aggregator.get_token_price(token_address, "USDC")
                    if price_info:
                        price_data[token_address] = price_info

            market_data = {
                'timestamp': datetime.now().isoformat(),
                'top_assets': top_assets,
                'gas_prices': gas_prices,
                'sentiment': sentiment_data,
                'prices': price_data,
                'assets_count': len(top_assets)
            }

            self.logger.info(f"Market data collected: {len(top_assets)} assets, {len(price_data)} prices, sentiment={bool(sentiment_data)}")
            return market_data

        except Exception as e:
            self.logger.error(f"Market data collection failed: {e}")
            return {}

    async def _validate_market_data(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate collected market data quality"""
        try:
            quality_metrics = {
                'overall_quality': 1.0,
                'data_completeness': 0.0,
                'price_quality': 0.0,
                'sentiment_quality': 0.0
            }

            # Check data completeness
            required_fields = ['top_assets', 'gas_prices', 'timestamp']
            present_fields = sum(1 for field in required_fields if field in market_data and market_data[field])
            quality_metrics['data_completeness'] = present_fields / len(required_fields)

            # Validate price data if available
            prices = market_data.get('prices', {})
            if prices:
                price_validations = []
                for token_addr, price_info in prices.items():
                    validation = await data_validator.validate_price_data(price_info)
                    price_validations.append(validation['quality_score'])
                quality_metrics['price_quality'] = sum(price_validations) / len(price_validations)
            else:
                quality_metrics['price_quality'] = 0.5  # Neutral if no price data

            # Validate sentiment data if available
            sentiment = market_data.get('sentiment', {})
            if sentiment:
                sentiment_validation = await data_validator.validate_sentiment_data({'data': sentiment})
                quality_metrics['sentiment_quality'] = sentiment_validation['quality_score']
            else:
                quality_metrics['sentiment_quality'] = 0.5  # Neutral if no sentiment data

            # Calculate overall quality
            quality_metrics['overall_quality'] = (
                quality_metrics['data_completeness'] * 0.4 +
                quality_metrics['price_quality'] * 0.4 +
                quality_metrics['sentiment_quality'] * 0.2
            )

            return quality_metrics

        except Exception as e:
            self.logger.error(f"Data validation failed: {e}")
            return {'overall_quality': 0.0}
    
    async def start(self):
        """Start the bot main loop with Phase 2 data systems and Phase 3 AI/ML"""
        self.logger.info("Starting Polygon MATIC Signal Bot - Phase 3 AI/ML Implementation")

        # Initialize and test data systems
        await self._initialize_data_systems()

        # Initialize and test ML systems
        await self._initialize_ml_systems()

        self.running = True

        while self.running:
            try:
                await self.run_cycle()

                # Sleep for configured interval (default 15 seconds)
                await asyncio.sleep(self.config.SCAN_INTERVAL)

            except KeyboardInterrupt:
                self.logger.info("Received shutdown signal")
                self.running = False
            except Exception as e:
                self.logger.error(f"Unexpected error in main loop: {e}")
                await asyncio.sleep(5)  # Brief pause before retry

        # Cleanup on exit
        await self._cleanup_data_systems()
    
    def stop(self):
        """Stop the bot"""
        self.logger.info("Stopping Signal Bot")
        self.running = False

    async def _initialize_data_systems(self):
        """Initialize and test all Phase 2 data collection systems"""
        self.logger.info("Initializing Phase 2 data systems...")

        try:
            # Test RPC connection
            rpc_health = await polygon_rpc.health_check()
            self.logger.info(f"RPC Health: {rpc_health.get('healthy', False)}")

            # Test cache system
            cache_health = await cache_manager.health_check()
            self.logger.info(f"Cache Health: Redis={cache_health.get('redis', {}).get('healthy', False)}, SQLite={cache_health.get('sqlite', {}).get('healthy', False)}")

            # Test data collectors with sample requests
            await self._test_data_collectors()

            self.data_systems_healthy = True
            self.logger.info("Phase 2 data systems initialized successfully")

        except Exception as e:
            self.logger.error(f"Data systems initialization failed: {e}")
            self.data_systems_healthy = False
            self.logger.warning("Continuing in degraded mode without real-time data")

    async def _test_data_collectors(self):
        """Test data collectors with sample requests"""
        try:
            # Test Simple Asset Provider (replaces deprecated subgraphs)
            qs_pairs = await simple_asset_provider.get_top_pairs(3)
            self.logger.info(f"Asset Provider (QuickSwap replacement) test: {len(qs_pairs)} pairs fetched")

            # Test Simple Asset Provider for pools
            uni_pools = await simple_asset_provider.get_top_pools(3)
            self.logger.info(f"Asset Provider (Uniswap replacement) test: {len(uni_pools)} pools fetched")

            # Test Alchemy Market client (replaces Coinglass)
            if alchemy_market_client:
                sentiment = await alchemy_market_client.get_market_sentiment_summary("MATIC")
                self.logger.info(f"Alchemy Market test: Sentiment data available = {bool(sentiment)}")
            else:
                self.logger.info("Alchemy Market test: Skipped (no API key)")

            # Test asset selector
            top_assets = await asset_selector.get_top_assets()
            self.logger.info(f"Asset selector test: {len(top_assets)} assets selected")

        except Exception as e:
            self.logger.warning(f"Data collectors test partial failure: {e}")

    async def _initialize_ml_systems(self):
        """Initialize and test Phase 3 ML systems"""
        self.logger.info("Initializing Phase 3 ML systems...")

        try:
            # Test inference engine
            inference_metrics = inference_engine.get_performance_metrics()
            self.logger.info(f"Inference engine initialized: {inference_metrics['models_loaded']}")

            # Load existing models if available
            load_results = inference_engine.multi_lstm.load_all_models()
            loaded_models = sum(1 for success in load_results.values() if success)
            total_models = len(load_results)

            self.logger.info(f"LSTM models loaded: {loaded_models}/{total_models}")

            # Test DeepSeek API connection
            try:
                from src.ml_models.deepseek_client import DeepSeekClient
                async with DeepSeekClient() as client:
                    # Simple test - analyze sentiment for MATIC
                    test_data = {
                        'price_change_24h': 1.5,
                        'volume_change': 2.0,
                        'liquidity_usd': 1000000
                    }
                    sentiment = await client.analyze_sentiment("MATIC/USDC", test_data)
                    deepseek_healthy = sentiment is not None

                self.logger.info(f"DeepSeek API test: {'✓' if deepseek_healthy else '✗'}")

            except Exception as e:
                self.logger.warning(f"DeepSeek API test failed: {e}")
                deepseek_healthy = False

            # Set ML systems health status
            self.ml_systems_healthy = loaded_models > 0 or deepseek_healthy

            if self.ml_systems_healthy:
                self.logger.info("Phase 3 ML systems initialized successfully")
            else:
                self.logger.warning("ML systems initialization incomplete - some features may be limited")

        except Exception as e:
            self.logger.error(f"ML systems initialization failed: {e}")
            self.ml_systems_healthy = False
            self.logger.warning("Continuing without ML predictions")

    async def _cleanup_data_systems(self):
        """Clean up Phase 2 data system resources"""
        self.logger.info("Cleaning up data systems...")

        try:
            await polygon_rpc.close()
            await simple_asset_provider.close()
            if alchemy_market_client:
                await alchemy_market_client.close()
            await cache_manager.close()
            self.logger.info("Data systems cleanup completed")

        except Exception as e:
            self.logger.error(f"Data systems cleanup error: {e}")

    async def _log_portfolio_performance(self):
        """Log portfolio performance summary"""
        try:
            portfolio_summary = self.portfolio_manager.get_portfolio_summary()

            # Log key metrics
            portfolio_value = portfolio_summary['portfolio_value']
            performance = portfolio_summary['performance']
            positions = portfolio_summary['positions']

            self.logger.info(f"Portfolio Performance Summary:")
            self.logger.info(f"  Total Value: ${portfolio_value['total_value']:.2f} "
                           f"(Return: {performance['total_return_pct']:.2f}%)")
            self.logger.info(f"  Open Positions: {positions['open_positions']} "
                           f"(Unrealized P&L: ${portfolio_value['unrealized_pnl']:.2f})")
            self.logger.info(f"  Trade Stats: {performance['total_trades']} trades, "
                           f"{performance['win_rate_pct']:.1f}% win rate")

            # Calculate and log performance metrics if we have trade history
            if performance['total_trades'] > 0:
                trade_history = self.portfolio_manager.get_trade_history()
                metrics = self.performance_analytics.calculate_performance_metrics(
                    trade_history,
                    self.portfolio_manager.initial_balance,
                    portfolio_value['total_value']
                )

                self.logger.info(f"  Advanced Metrics: Sharpe={metrics.sharpe_ratio:.3f}, "
                               f"Max DD={metrics.max_drawdown_pct:.2f}%, "
                               f"Avg Hold={metrics.avg_hold_time_minutes:.1f}min")

        except Exception as e:
            self.logger.error(f"Portfolio performance logging failed: {e}")

    async def _log_portfolio_performance(self):
        """Log portfolio performance summary"""
        try:
            portfolio_summary = self.portfolio_manager.get_portfolio_summary()

            # Log key metrics
            portfolio_value = portfolio_summary['portfolio_value']
            performance = portfolio_summary['performance']
            positions = portfolio_summary['positions']

            self.logger.info(f"Portfolio Performance Summary:")
            self.logger.info(f"  Total Value: ${portfolio_value['total_value']:.2f} "
                           f"(Return: {performance['total_return_pct']:.2f}%)")
            self.logger.info(f"  Open Positions: {positions['open_positions']} "
                           f"(Unrealized P&L: ${portfolio_value['unrealized_pnl']:.2f})")
            self.logger.info(f"  Trade Stats: {performance['total_trades']} trades, "
                           f"{performance['win_rate_pct']:.1f}% win rate")

            # Calculate and log performance metrics if we have trade history
            if performance['total_trades'] > 0:
                trade_history = self.portfolio_manager.get_trade_history()
                metrics = self.performance_analytics.calculate_performance_metrics(
                    trade_history,
                    self.portfolio_manager.initial_balance,
                    portfolio_value['total_value']
                )

                self.logger.info(f"  Advanced Metrics: Sharpe={metrics.sharpe_ratio:.3f}, "
                               f"Max DD={metrics.max_drawdown_pct:.2f}%, "
                               f"Avg Hold={metrics.avg_hold_time_minutes:.1f}min")

        except Exception as e:
            self.logger.error(f"Portfolio performance logging failed: {e}")


async def main():
    """Main entry point"""
    bot = SignalBot()
    
    try:
        await bot.start()
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    finally:
        bot.stop()
        print("Signal Bot stopped")


if __name__ == "__main__":
    asyncio.run(main())
