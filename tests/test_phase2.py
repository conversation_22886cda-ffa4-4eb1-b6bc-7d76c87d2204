"""
Comprehensive tests for Phase 2: Data Infrastructure
Tests all data collectors, caching, validation, and aggregation systems
"""

import asyncio
import pytest
import json
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

# Import all Phase 2 components
from src.data_collectors.polygon_rpc import PolygonRPCClient
from src.data_collectors.dex_collectors import QuickSwapCollector, UniswapV3Collector
from src.data_collectors.coinglass_client import CoinglassClient
from src.data_collectors.cache_manager import CacheManager
from src.data_collectors.price_aggregator import PriceAggregator
from src.data_collectors.asset_selector import AssetSelector
from src.data_collectors.data_validator import DataValidator


class TestPolygonRPC:
    """Test Polygon RPC client functionality"""
    
    @pytest.fixture
    def rpc_client(self):
        return PolygonRPCClient("https://polygon-rpc.com")
    
    def test_rpc_client_initialization(self, rpc_client):
        """Test RPC client initializes correctly"""
        assert rpc_client.rpc_url == "https://polygon-rpc.com"
        assert rpc_client.retry_attempts == 3
        assert rpc_client.max_connections == 5
    
    @pytest.mark.asyncio
    async def test_health_check(self, rpc_client):
        """Test RPC health check"""
        with patch.object(rpc_client, 'get_latest_block') as mock_block:
            mock_block.return_value = {'number': 12345}
            
            health = await rpc_client.health_check()
            
            assert 'healthy' in health
            assert 'latest_block' in health
            assert 'response_time_ms' in health
    
    @pytest.mark.asyncio
    async def test_gas_price_estimation(self, rpc_client):
        """Test gas price estimation"""
        with patch.object(rpc_client.web3.eth, 'gas_price', 30000000000):  # 30 Gwei
            gas_prices = await rpc_client.get_gas_price()
            
            assert 'standard' in gas_prices
            assert 'fast' in gas_prices
            assert 'instant' in gas_prices
            assert gas_prices['fast'] > gas_prices['standard']
            assert gas_prices['instant'] > gas_prices['fast']


class TestDEXCollectors:
    """Test DEX data collectors"""
    
    @pytest.fixture
    def quickswap_collector(self):
        return QuickSwapCollector()
    
    @pytest.fixture
    def uniswap_collector(self):
        return UniswapV3Collector()
    
    @pytest.mark.asyncio
    async def test_quickswap_top_pairs(self, quickswap_collector):
        """Test QuickSwap top pairs fetching"""
        mock_response = {
            'pairs': [
                {
                    'id': '0x123',
                    'token0': {'id': '0xabc', 'symbol': 'MATIC', 'name': 'Polygon', 'decimals': '18'},
                    'token1': {'id': '0xdef', 'symbol': 'USDC', 'name': 'USD Coin', 'decimals': '6'},
                    'reserveUSD': '1000000',
                    'volumeUSD': '500000',
                    'token0Price': '0.8',
                    'token1Price': '1.25',
                    'txCount': '1000',
                    'createdAtTimestamp': '1640995200'
                }
            ]
        }
        
        with patch.object(quickswap_collector, '_execute_query', return_value=mock_response):
            pairs = await quickswap_collector.get_top_pairs(10)
            
            assert len(pairs) == 1
            assert pairs[0]['id'] == '0x123'
            assert pairs[0]['token0']['symbol'] == 'MATIC'
    
    @pytest.mark.asyncio
    async def test_uniswap_v3_top_pools(self, uniswap_collector):
        """Test Uniswap V3 top pools fetching"""
        mock_response = {
            'pools': [
                {
                    'id': '0x456',
                    'token0': {'id': '0xabc', 'symbol': 'MATIC', 'name': 'Polygon', 'decimals': '18'},
                    'token1': {'id': '0xdef', 'symbol': 'USDC', 'name': 'USD Coin', 'decimals': '6'},
                    'totalValueLockedUSD': '2000000',
                    'volumeUSD': '800000',
                    'token0Price': '0.8',
                    'token1Price': '1.25',
                    'feeTier': '3000',
                    'txCount': '2000',
                    'createdAtTimestamp': '1640995200'
                }
            ]
        }
        
        with patch.object(uniswap_collector, '_execute_query', return_value=mock_response):
            pools = await uniswap_collector.get_top_pools(10)
            
            assert len(pools) == 1
            assert pools[0]['id'] == '0x456'
            assert pools[0]['feeTier'] == '3000'


class TestCoinglassClient:
    """Test Coinglass API client"""
    
    @pytest.fixture
    def coinglass_client(self):
        return CoinglassClient("test_api_key")
    
    @pytest.mark.asyncio
    async def test_liquidation_data(self, coinglass_client):
        """Test liquidation data fetching"""
        mock_response = {
            'totalLiquidation': 5000000,
            'longLiquidation': 3000000,
            'shortLiquidation': 2000000
        }
        
        with patch.object(coinglass_client, '_make_request', return_value=mock_response):
            data = await coinglass_client.get_liquidation_data('MATIC', '24h')
            
            assert data['totalLiquidation'] == 5000000
            assert data['longLiquidation'] == 3000000
    
    @pytest.mark.asyncio
    async def test_market_sentiment_summary(self, coinglass_client):
        """Test comprehensive market sentiment data"""
        mock_liquidation = {'totalLiquidation': 1000000}
        mock_funding = [{'rate': '0.01'}, {'rate': '0.02'}]
        mock_oi = {'totalOpenInterest': 50000000}
        mock_ls = {'avgRatio': 1.2}
        mock_fg = {'value': 75, 'classification': 'Greed'}
        
        with patch.object(coinglass_client, 'get_liquidation_data', return_value=mock_liquidation), \
             patch.object(coinglass_client, 'get_funding_rates', return_value=mock_funding), \
             patch.object(coinglass_client, 'get_open_interest', return_value=mock_oi), \
             patch.object(coinglass_client, 'get_long_short_ratio', return_value=mock_ls), \
             patch.object(coinglass_client, 'get_fear_greed_index', return_value=mock_fg):
            
            summary = await coinglass_client.get_market_sentiment_summary('MATIC')
            
            assert summary['symbol'] == 'MATIC'
            assert summary['liquidation_24h'] == 1000000
            assert summary['avg_funding_rate'] == 0.015  # Average of 0.01 and 0.02
            assert summary['fear_greed_index'] == 75


class TestCacheManager:
    """Test caching system"""
    
    @pytest.fixture
    def cache_manager(self):
        return CacheManager()
    
    @pytest.mark.asyncio
    async def test_cache_set_get(self, cache_manager):
        """Test basic cache operations"""
        test_data = {'price': 0.85, 'timestamp': datetime.now().isoformat()}
        
        # Set cache
        success = await cache_manager.set('test_key', test_data, 60)
        assert success
        
        # Get cache
        retrieved_data = await cache_manager.get('test_key')
        assert retrieved_data is not None
        assert retrieved_data['price'] == 0.85
    
    @pytest.mark.asyncio
    async def test_cache_expiration(self, cache_manager):
        """Test cache TTL functionality"""
        test_data = {'test': 'data'}
        
        # Set with very short TTL
        await cache_manager.set('expire_test', test_data, 1)
        
        # Should exist immediately
        data = await cache_manager.get('expire_test')
        assert data is not None
        
        # Wait for expiration
        await asyncio.sleep(2)
        
        # Should be expired
        data = await cache_manager.get('expire_test')
        assert data is None


class TestPriceAggregator:
    """Test price aggregation system"""
    
    @pytest.fixture
    def price_aggregator(self):
        return PriceAggregator()
    
    @pytest.mark.asyncio
    async def test_price_aggregation_single_source(self, price_aggregator):
        """Test price aggregation with single source"""
        mock_sources = [
            {
                'source': 'quickswap',
                'price': 0.85,
                'liquidity_usd': 1000000,
                'timestamp': datetime.now().isoformat()
            }
        ]
        
        with patch.object(price_aggregator, '_collect_price_sources', return_value=mock_sources):
            result = await price_aggregator.get_token_price('0x123', 'USDC')
            
            assert result is not None
            assert result['price'] == 0.85
            assert result['confidence'] == 0.7  # Single source confidence
            assert result['aggregation_method'] == 'single_source'
    
    @pytest.mark.asyncio
    async def test_price_aggregation_multiple_sources(self, price_aggregator):
        """Test price aggregation with multiple sources"""
        mock_sources = [
            {
                'source': 'quickswap',
                'price': 0.85,
                'liquidity_usd': 1000000,
                'timestamp': datetime.now().isoformat()
            },
            {
                'source': 'uniswap_v3',
                'price': 0.87,
                'liquidity_usd': 2000000,
                'timestamp': datetime.now().isoformat()
            }
        ]
        
        with patch.object(price_aggregator, '_collect_price_sources', return_value=mock_sources):
            result = await price_aggregator.get_token_price('0x123', 'USDC')
            
            assert result is not None
            assert 0.85 <= result['price'] <= 0.87  # Should be between the two prices
            assert result['aggregation_method'] == 'weighted_average'
            assert len(result['sources']) == 2


class TestAssetSelector:
    """Test asset selection system"""
    
    @pytest.fixture
    def asset_selector(self):
        return AssetSelector()
    
    @pytest.mark.asyncio
    async def test_asset_filtering(self, asset_selector):
        """Test asset filtering logic"""
        mock_assets = [
            {
                'token0_symbol': 'MATIC',
                'token1_symbol': 'USDC',
                'liquidity_usd': 2000000,
                'volume_24h_usd': 500000,
                'token0_address': '0x123',
                'token1_address': '0x456'
            },
            {
                'token0_symbol': 'SCAM',
                'token1_symbol': 'TOKEN',
                'liquidity_usd': 50000,  # Below minimum
                'volume_24h_usd': 10000,
                'token0_address': '0x789',
                'token1_address': '0xabc'
            }
        ]
        
        filtered = await asset_selector._filter_assets(mock_assets)
        
        # Should filter out the low liquidity asset
        assert len(filtered) == 1
        assert filtered[0]['token0_symbol'] == 'MATIC'


class TestDataValidator:
    """Test data validation system"""
    
    @pytest.fixture
    def data_validator(self):
        return DataValidator()
    
    @pytest.mark.asyncio
    async def test_price_data_validation(self, data_validator):
        """Test price data validation"""
        valid_price_data = {
            'price': 0.85,
            'timestamp': datetime.now().isoformat(),
            'sources': [{'source': 'quickswap', 'price': 0.85}],
            'confidence': 0.8
        }
        
        result = await data_validator.validate_price_data(valid_price_data)
        
        assert result['valid'] is True
        assert result['quality_score'] > 0.5
        assert len(result['errors']) == 0
    
    @pytest.mark.asyncio
    async def test_price_anomaly_detection(self, data_validator):
        """Test price anomaly detection"""
        historical_prices = [0.80, 0.82, 0.81, 0.83, 0.79]
        current_price = 1.50  # Significant spike
        
        result = await data_validator.detect_price_anomalies(current_price, historical_prices)
        
        assert result['is_anomaly'] is True
        assert result['anomaly_type'] in ['price_spike', 'extreme_movement']
        assert result['severity'] > 1.0


@pytest.mark.asyncio
async def test_phase2_integration():
    """Integration test for Phase 2 components"""
    # Test that all components can be imported and initialized
    rpc_client = PolygonRPCClient()
    quickswap = QuickSwapCollector()
    uniswap = UniswapV3Collector()
    coinglass = CoinglassClient()
    cache_mgr = CacheManager()
    price_agg = PriceAggregator()
    asset_sel = AssetSelector()
    validator = DataValidator()
    
    # Test basic functionality
    assert rpc_client is not None
    assert quickswap is not None
    assert uniswap is not None
    assert coinglass is not None
    assert cache_mgr is not None
    assert price_agg is not None
    assert asset_sel is not None
    assert validator is not None
    
    # Test health checks where available
    rpc_health = await rpc_client.health_check()
    cache_health = await cache_mgr.health_check()
    
    assert 'healthy' in rpc_health
    assert 'redis' in cache_health or 'sqlite' in cache_health


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
